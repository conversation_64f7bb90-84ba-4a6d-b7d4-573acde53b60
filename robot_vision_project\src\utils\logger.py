"""
日志管理器
提供统一的日志记录功能
"""
import logging
import os
import sys
from datetime import datetime
from typing import Optional, Dict, Any
import traceback
from functools import wraps


class RobotVisionLogger:
    """机器人视觉系统日志管理器"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_logger()
            self._initialized = True
    
    def _setup_logger(self):
        """设置日志配置"""
        # 创建日志目录
        log_dir = os.path.join(os.path.dirname(__file__), '../../logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        
        # 文件处理器
        log_file = os.path.join(log_dir, f'robot_vision_{datetime.now().strftime("%Y%m%d")}.log')
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        
        # 错误文件处理器
        error_file = os.path.join(log_dir, f'robot_vision_error_{datetime.now().strftime("%Y%m%d")}.log')
        error_handler = logging.FileHandler(error_file, encoding='utf-8')
        error_handler.setFormatter(formatter)
        error_handler.setLevel(logging.ERROR)
        
        # 配置根日志器
        self.logger = logging.getLogger('RobotVision')
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        self.logger.addHandler(error_handler)
    
    def get_logger(self, name: str = None) -> logging.Logger:
        """获取指定名称的日志器"""
        if name:
            return logging.getLogger(f'RobotVision.{name}')
        return self.logger
    
    def log_exception(self, exception: Exception, context: Dict[str, Any] = None):
        """记录异常信息"""
        error_info = {
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'traceback': traceback.format_exc(),
            'context': context or {}
        }
        
        self.logger.error(f"Exception occurred: {error_info}")
    
    def log_performance(self, operation: str, duration: float, context: Dict[str, Any] = None):
        """记录性能信息"""
        perf_info = {
            'operation': operation,
            'duration_ms': duration * 1000,
            'context': context or {}
        }
        
        self.logger.info(f"Performance: {perf_info}")


def log_exceptions(logger_name: str = None):
    """异常记录装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger = RobotVisionLogger().get_logger(logger_name)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Exception in {func.__name__}: {str(e)}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                raise
        return wrapper
    return decorator


def log_performance(logger_name: str = None):
    """性能记录装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            import time
            logger = RobotVisionLogger().get_logger(logger_name)
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"{func.__name__} completed in {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{func.__name__} failed after {duration:.3f}s: {str(e)}")
                raise
        return wrapper
    return decorator


# 全局日志器实例
logger_instance = RobotVisionLogger()
