"""
系统监控器
提供性能监控、资源使用跟踪和系统状态监控功能
"""
import time
import psutil
import threading
import json
import os
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from collections import deque
import statistics

from .logger import RobotVisionLogger, log_exceptions


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_usage_percent: float
    gpu_memory_mb: Optional[float] = None
    gpu_utilization: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    operation: str
    duration_ms: float
    success: bool
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.system_metrics = deque(maxlen=max_history)
        self.performance_metrics = deque(maxlen=max_history)
        self.custom_metrics = {}
        self.logger = RobotVisionLogger().get_logger('MetricsCollector')
    
    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / (1024 * 1024)
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_usage_percent = disk.percent
            
            # GPU指标（如果可用）
            gpu_memory_mb = None
            gpu_utilization = None
            
            try:
                import GPUtil
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu = gpus[0]  # 使用第一个GPU
                    gpu_memory_mb = gpu.memoryUsed
                    gpu_utilization = gpu.load * 100
            except ImportError:
                pass  # GPU监控不可用
            
            metrics = SystemMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                disk_usage_percent=disk_usage_percent,
                gpu_memory_mb=gpu_memory_mb,
                gpu_utilization=gpu_utilization
            )
            
            self.system_metrics.append(metrics)
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to collect system metrics: {str(e)}")
            return None
    
    def add_performance_metric(self, operation: str, duration_ms: float, 
                             success: bool, error_message: str = None):
        """添加性能指标"""
        metric = PerformanceMetrics(
            timestamp=time.time(),
            operation=operation,
            duration_ms=duration_ms,
            success=success,
            error_message=error_message
        )
        
        self.performance_metrics.append(metric)
        self.logger.debug(f"Performance metric added: {operation} - {duration_ms:.2f}ms")
    
    def add_custom_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """添加自定义指标"""
        if name not in self.custom_metrics:
            self.custom_metrics[name] = deque(maxlen=self.max_history)
        
        metric_data = {
            'timestamp': time.time(),
            'value': value,
            'tags': tags or {}
        }
        
        self.custom_metrics[name].append(metric_data)
    
    def get_system_stats(self, window_minutes: int = 5) -> Dict[str, Any]:
        """获取系统统计信息"""
        cutoff_time = time.time() - (window_minutes * 60)
        recent_metrics = [m for m in self.system_metrics if m.timestamp >= cutoff_time]
        
        if not recent_metrics:
            return {}
        
        cpu_values = [m.cpu_percent for m in recent_metrics]
        memory_values = [m.memory_percent for m in recent_metrics]
        
        return {
            'cpu': {
                'avg': statistics.mean(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values),
                'current': recent_metrics[-1].cpu_percent
            },
            'memory': {
                'avg': statistics.mean(memory_values),
                'max': max(memory_values),
                'min': min(memory_values),
                'current': recent_metrics[-1].memory_percent
            },
            'sample_count': len(recent_metrics),
            'window_minutes': window_minutes
        }
    
    def get_performance_stats(self, operation: str = None, 
                            window_minutes: int = 5) -> Dict[str, Any]:
        """获取性能统计信息"""
        cutoff_time = time.time() - (window_minutes * 60)
        
        # 过滤指标
        filtered_metrics = []
        for m in self.performance_metrics:
            if m.timestamp >= cutoff_time:
                if operation is None or m.operation == operation:
                    filtered_metrics.append(m)
        
        if not filtered_metrics:
            return {}
        
        durations = [m.duration_ms for m in filtered_metrics]
        success_count = sum(1 for m in filtered_metrics if m.success)
        
        return {
            'operation': operation or 'all',
            'total_calls': len(filtered_metrics),
            'success_count': success_count,
            'success_rate': success_count / len(filtered_metrics) * 100,
            'avg_duration_ms': statistics.mean(durations),
            'max_duration_ms': max(durations),
            'min_duration_ms': min(durations),
            'window_minutes': window_minutes
        }


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, collection_interval: float = 5.0):
        self.collection_interval = collection_interval
        self.metrics_collector = MetricsCollector()
        self.logger = RobotVisionLogger().get_logger('SystemMonitor')
        
        # 监控线程
        self.monitoring_thread = None
        self.monitoring_active = False
        
        # 告警配置
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_usage_percent': 90.0
        }
        
        # 告警回调
        self.alert_callbacks = []
    
    @log_exceptions('SystemMonitor')
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring_active:
            self.logger.warning("Monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
        self.logger.info("System monitoring started")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring_active:
            return
        
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=2.0)
        
        self.logger.info("System monitoring stopped")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 收集系统指标
                metrics = self.metrics_collector.collect_system_metrics()
                
                if metrics:
                    # 检查告警条件
                    self._check_alerts(metrics)
                
                time.sleep(self.collection_interval)
                
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {str(e)}")
                time.sleep(self.collection_interval)
    
    def _check_alerts(self, metrics: SystemMetrics):
        """检查告警条件"""
        alerts = []
        
        # CPU告警
        if metrics.cpu_percent > self.alert_thresholds['cpu_percent']:
            alerts.append({
                'type': 'cpu_high',
                'message': f"High CPU usage: {metrics.cpu_percent:.1f}%",
                'value': metrics.cpu_percent,
                'threshold': self.alert_thresholds['cpu_percent']
            })
        
        # 内存告警
        if metrics.memory_percent > self.alert_thresholds['memory_percent']:
            alerts.append({
                'type': 'memory_high',
                'message': f"High memory usage: {metrics.memory_percent:.1f}%",
                'value': metrics.memory_percent,
                'threshold': self.alert_thresholds['memory_percent']
            })
        
        # 磁盘告警
        if metrics.disk_usage_percent > self.alert_thresholds['disk_usage_percent']:
            alerts.append({
                'type': 'disk_high',
                'message': f"High disk usage: {metrics.disk_usage_percent:.1f}%",
                'value': metrics.disk_usage_percent,
                'threshold': self.alert_thresholds['disk_usage_percent']
            })
        
        # 触发告警回调
        for alert in alerts:
            self.logger.warning(alert['message'])
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"Alert callback error: {str(e)}")
    
    def add_alert_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
    
    def set_alert_threshold(self, metric: str, threshold: float):
        """设置告警阈值"""
        if metric in self.alert_thresholds:
            self.alert_thresholds[metric] = threshold
            self.logger.info(f"Alert threshold updated: {metric} = {threshold}")
        else:
            self.logger.warning(f"Unknown metric for alert threshold: {metric}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前系统状态"""
        if not self.metrics_collector.system_metrics:
            return {'status': 'no_data'}
        
        latest_metrics = self.metrics_collector.system_metrics[-1]
        stats = self.metrics_collector.get_system_stats()
        
        return {
            'status': 'active' if self.monitoring_active else 'inactive',
            'current_metrics': latest_metrics.to_dict(),
            'stats': stats,
            'alert_thresholds': self.alert_thresholds,
            'collection_interval': self.collection_interval
        }
    
    def export_metrics(self, filepath: str, format: str = 'json'):
        """导出指标数据"""
        try:
            data = {
                'system_metrics': [m.to_dict() for m in self.metrics_collector.system_metrics],
                'performance_metrics': [m.to_dict() for m in self.metrics_collector.performance_metrics],
                'custom_metrics': {
                    name: list(metrics) 
                    for name, metrics in self.metrics_collector.custom_metrics.items()
                },
                'export_timestamp': time.time(),
                'export_date': time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            if format.lower() == 'json':
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"Unsupported export format: {format}")
            
            self.logger.info(f"Metrics exported to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Failed to export metrics: {str(e)}")
            raise


# 全局监控器实例
system_monitor = SystemMonitor()


class PerformanceProfiler:
    """性能分析器"""

    def __init__(self):
        self.logger = RobotVisionLogger().get_logger('PerformanceProfiler')
        self.active_operations = {}
        self.operation_stats = {}

    def start_operation(self, operation_name: str, context: Dict[str, Any] = None) -> str:
        """开始操作计时"""
        operation_id = f"{operation_name}_{int(time.time() * 1000000)}"

        self.active_operations[operation_id] = {
            'name': operation_name,
            'start_time': time.time(),
            'context': context or {}
        }

        return operation_id

    def end_operation(self, operation_id: str, success: bool = True,
                     error_message: str = None) -> float:
        """结束操作计时"""
        if operation_id not in self.active_operations:
            self.logger.warning(f"Unknown operation ID: {operation_id}")
            return 0.0

        operation = self.active_operations.pop(operation_id)
        duration = time.time() - operation['start_time']
        duration_ms = duration * 1000

        # 记录到系统监控器
        system_monitor.metrics_collector.add_performance_metric(
            operation['name'], duration_ms, success, error_message
        )

        # 更新操作统计
        self._update_operation_stats(operation['name'], duration_ms, success)

        self.logger.debug(f"Operation {operation['name']} completed in {duration_ms:.2f}ms")
        return duration

    def _update_operation_stats(self, operation_name: str, duration_ms: float, success: bool):
        """更新操作统计"""
        if operation_name not in self.operation_stats:
            self.operation_stats[operation_name] = {
                'total_calls': 0,
                'success_calls': 0,
                'total_duration_ms': 0.0,
                'min_duration_ms': float('inf'),
                'max_duration_ms': 0.0,
                'durations': deque(maxlen=100)  # 保留最近100次调用
            }

        stats = self.operation_stats[operation_name]
        stats['total_calls'] += 1
        if success:
            stats['success_calls'] += 1

        stats['total_duration_ms'] += duration_ms
        stats['min_duration_ms'] = min(stats['min_duration_ms'], duration_ms)
        stats['max_duration_ms'] = max(stats['max_duration_ms'], duration_ms)
        stats['durations'].append(duration_ms)

    def get_operation_stats(self, operation_name: str = None) -> Dict[str, Any]:
        """获取操作统计信息"""
        if operation_name:
            if operation_name not in self.operation_stats:
                return {}

            stats = self.operation_stats[operation_name]
            durations = list(stats['durations'])

            return {
                'operation': operation_name,
                'total_calls': stats['total_calls'],
                'success_calls': stats['success_calls'],
                'success_rate': stats['success_calls'] / max(1, stats['total_calls']) * 100,
                'avg_duration_ms': stats['total_duration_ms'] / max(1, stats['total_calls']),
                'min_duration_ms': stats['min_duration_ms'] if stats['min_duration_ms'] != float('inf') else 0,
                'max_duration_ms': stats['max_duration_ms'],
                'recent_avg_ms': statistics.mean(durations) if durations else 0,
                'recent_samples': len(durations)
            }
        else:
            # 返回所有操作的统计
            return {
                name: self.get_operation_stats(name)
                for name in self.operation_stats.keys()
            }

    def profile_function(self, func_name: str = None):
        """函数性能分析装饰器"""
        def decorator(func):
            operation_name = func_name or f"{func.__module__}.{func.__name__}"

            def wrapper(*args, **kwargs):
                operation_id = self.start_operation(operation_name)
                try:
                    result = func(*args, **kwargs)
                    self.end_operation(operation_id, success=True)
                    return result
                except Exception as e:
                    self.end_operation(operation_id, success=False, error_message=str(e))
                    raise

            return wrapper
        return decorator


# 全局性能分析器实例
performance_profiler = PerformanceProfiler()


def profile_operation(operation_name: str = None):
    """操作性能分析装饰器"""
    return performance_profiler.profile_function(operation_name)
