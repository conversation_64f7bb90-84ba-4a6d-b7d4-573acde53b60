# 部署指南

## 生产环境部署

### 系统要求

#### 硬件要求
- **CPU**: Intel i5 或 AMD Ryzen 5 以上
- **内存**: 8GB RAM 最低，16GB 推荐
- **存储**: 50GB 可用空间
- **GPU**: NVIDIA GTX 1060 或更高（可选，用于加速）
- **摄像头**: USB 3.0 或网络摄像头

#### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows 10+
- **Python**: 3.8-3.11
- **CUDA**: 11.0+ (GPU加速时)
- **Docker**: 20.10+ (容器化部署时)

### 部署方式

#### 1. 直接部署

##### 步骤 1: 环境准备
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip python3-venv git

# CentOS/RHEL
sudo yum update
sudo yum install python3 python3-pip git

# 创建系统用户
sudo useradd -m -s /bin/bash robotvision
sudo usermod -aG video robotvision  # 摄像头权限
```

##### 步骤 2: 代码部署
```bash
# 切换到部署用户
sudo su - robotvision

# 克隆代码
git clone <repository-url> /opt/robot_vision_system
cd /opt/robot_vision_system

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

##### 步骤 3: 配置文件
```bash
# 复制配置模板
cp config/config.yaml config/production.yaml

# 编辑生产配置
nano config/production.yaml
```

生产配置示例：
```yaml
vision:
  model_path: "/opt/robot_vision_system/models/yolov8n.pt"
  confidence_threshold: 0.6
  camera_id: 0

planning:
  priority_map:
    0: 10
    1: 8
    2: 9
  max_concurrent_tasks: 3

control:
  max_speed: 0.8
  safety_bounds:
    x: [-3.0, 3.0]
    y: [-3.0, 3.0]
    z: [0.0, 2.5]

logging:
  level: "INFO"
  file: "/var/log/robot_vision/system.log"
```

##### 步骤 4: 系统服务
创建 systemd 服务文件：

```bash
sudo nano /etc/systemd/system/robot-vision.service
```

```ini
[Unit]
Description=Robot Vision System
After=network.target

[Service]
Type=simple
User=robotvision
Group=robotvision
WorkingDirectory=/opt/robot_vision_system
Environment=PATH=/opt/robot_vision_system/venv/bin
ExecStart=/opt/robot_vision_system/venv/bin/python src/main.py --config config/production.yaml
Restart=always
RestartSec=10

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=robot-vision

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/var/log/robot_vision /opt/robot_vision_system/logs

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable robot-vision
sudo systemctl start robot-vision
sudo systemctl status robot-vision
```

#### 2. Docker 部署

##### Dockerfile
```dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p /app/logs

# 设置环境变量
ENV PYTHONPATH=/app

# 暴露端口（如果有Web界面）
EXPOSE 8080

# 启动命令
CMD ["python", "src/main.py", "--config", "config/config.yaml"]
```

##### docker-compose.yml
```yaml
version: '3.8'

services:
  robot-vision:
    build: .
    container_name: robot-vision-system
    restart: unless-stopped
    
    # 设备访问
    devices:
      - /dev/video0:/dev/video0  # 摄像头
    
    # 环境变量
    environment:
      - ROBOT_VISION_LOG_LEVEL=INFO
      - ROBOT_VISION_CONFIDENCE_THRESHOLD=0.6
    
    # 卷挂载
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
      - ./data:/app/data
      - ./models:/app/models
    
    # 网络配置
    networks:
      - robot-vision-net
    
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8080/health')"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  robot-vision-net:
    driver: bridge
```

部署命令：
```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f robot-vision

# 停止服务
docker-compose down
```

#### 3. Kubernetes 部署

##### deployment.yaml
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: robot-vision-system
  labels:
    app: robot-vision
spec:
  replicas: 1
  selector:
    matchLabels:
      app: robot-vision
  template:
    metadata:
      labels:
        app: robot-vision
    spec:
      containers:
      - name: robot-vision
        image: robot-vision:latest
        ports:
        - containerPort: 8080
        env:
        - name: ROBOT_VISION_LOG_LEVEL
          value: "INFO"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: logs-volume
          mountPath: /app/logs
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
      volumes:
      - name: config-volume
        configMap:
          name: robot-vision-config
      - name: logs-volume
        persistentVolumeClaim:
          claimName: robot-vision-logs
```

### 监控和维护

#### 1. 日志管理

##### 日志轮转配置
```bash
sudo nano /etc/logrotate.d/robot-vision
```

```
/var/log/robot_vision/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 robotvision robotvision
    postrotate
        systemctl reload robot-vision
    endscript
}
```

#### 2. 监控配置

##### Prometheus 监控
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'robot-vision'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 15s
```

##### Grafana 仪表板
创建监控面板显示：
- 系统资源使用率
- 检测性能指标
- 任务执行统计
- 错误率趋势

#### 3. 备份策略

##### 自动备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/robot_vision"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz /opt/robot_vision_system/config/

# 备份日志文件
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz /var/log/robot_vision/

# 备份模型文件
tar -czf $BACKUP_DIR/models_$DATE.tar.gz /opt/robot_vision_system/models/

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

设置定时任务：
```bash
# 添加到 crontab
0 2 * * * /opt/robot_vision_system/scripts/backup.sh
```

### 性能优化

#### 1. 系统级优化

##### CPU 优化
```bash
# 设置CPU调度策略
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# 禁用不必要的服务
sudo systemctl disable bluetooth
sudo systemctl disable cups
```

##### 内存优化
```bash
# 调整内存参数
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf
```

#### 2. 应用级优化

##### 配置优化
```yaml
# 生产优化配置
vision:
  confidence_threshold: 0.7  # 提高阈值减少误检
  input_size: [416, 416]     # 降低分辨率提高速度

planning:
  planning_horizon: 5        # 限制规划范围
  max_concurrent_tasks: 2    # 控制并发数

control:
  control_frequency: 50.0    # 降低控制频率
```

### 安全配置

#### 1. 网络安全
```bash
# 防火墙配置
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 8080/tcp  # 应用端口
```

#### 2. 访问控制
```bash
# 限制文件权限
chmod 600 config/production.yaml
chown robotvision:robotvision config/production.yaml

# 设置日志目录权限
chmod 755 /var/log/robot_vision
chown robotvision:robotvision /var/log/robot_vision
```

### 故障排除

#### 常见问题

1. **服务启动失败**
```bash
# 检查服务状态
sudo systemctl status robot-vision

# 查看详细日志
sudo journalctl -u robot-vision -f

# 检查配置文件
python -c "import yaml; yaml.safe_load(open('config/production.yaml'))"
```

2. **摄像头访问问题**
```bash
# 检查摄像头设备
ls -la /dev/video*

# 测试摄像头
v4l2-ctl --list-devices

# 检查权限
groups robotvision
```

3. **性能问题**
```bash
# 监控资源使用
htop
nvidia-smi  # GPU使用情况

# 检查磁盘空间
df -h

# 分析日志
tail -f /var/log/robot_vision/system.log
```

### 升级和维护

#### 版本升级流程
1. 备份当前版本
2. 停止服务
3. 更新代码
4. 更新依赖
5. 迁移配置
6. 测试新版本
7. 启动服务
8. 验证功能

```bash
# 升级脚本示例
#!/bin/bash
set -e

echo "Starting upgrade process..."

# 备份
./scripts/backup.sh

# 停止服务
sudo systemctl stop robot-vision

# 更新代码
git pull origin main

# 更新依赖
source venv/bin/activate
pip install -r requirements.txt

# 启动服务
sudo systemctl start robot-vision

# 验证
sleep 10
sudo systemctl status robot-vision

echo "Upgrade completed successfully!"
```
