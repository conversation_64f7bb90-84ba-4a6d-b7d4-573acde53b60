# Robot Vision System

一个完整的机器人视觉系统，集成了目标检测、任务规划、路径规划和机器人控制功能。

## 🚀 特性

- **高级目标检测**: 基于YOLO的实时目标检测，支持批处理和多模型
- **智能任务规划**: 动态优先级调整、任务状态管理和执行监控
- **先进路径规划**: 支持A*、RRT算法，包含障碍物避让和路径优化
- **精确机器人控制**: PID控制、运动学计算、安全检查和实时反馈
- **完整监控系统**: 性能监控、资源跟踪、告警机制
- **强大配置管理**: 热重载、环境变量支持、配置验证
- **全面错误处理**: 自定义异常、日志记录、故障恢复
- **完整测试体系**: 单元测试、集成测试、性能测试

## 📁 项目结构

```
robot_vision_project/
├── src/                          # 源代码
│   ├── vision/                   # 计算机视觉模块
│   │   ├── object_detector.py    # 目标检测器
│   │   └── image_processor.py    # 图像处理器
│   ├── planning/                 # 任务规划模块
│   │   ├── task_planner.py       # 任务规划器
│   │   └── path_planner.py       # 路径规划器
│   ├── control/                  # 机器人控制模块
│   │   └── robot_controller.py   # 机器人控制器
│   ├── utils/                    # 工具模块
│   │   ├── config_loader.py      # 配置加载器
│   │   ├── logger.py             # 日志管理器
│   │   ├── monitor.py            # 系统监控器
│   │   └── exceptions.py         # 自定义异常
│   └── main.py                   # 主程序
├── config/                       # 配置文件
│   └── config.yaml              # 主配置文件
├── tests/                        # 测试文件
│   ├── test_object_detector.py   # 目标检测测试
│   ├── test_path_planner.py      # 路径规划测试
│   ├── test_task_planner.py      # 任务规划测试
│   └── run_tests.py             # 测试运行器
├── logs/                         # 日志文件
├── data/                         # 数据存储
├── requirements.txt              # 依赖列表
└── README.md                     # 项目文档
```

## 🛠️ 安装

### 环境要求
- Python 3.8+
- CUDA 11.0+ (可选，用于GPU加速)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd robot_vision_project
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **下载模型文件**
```bash
# YOLO模型会在首次运行时自动下载
# 或手动下载到项目目录
wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt
```

## 🚀 快速开始

### 基本运行
```bash
python src/main.py
```

### 带配置文件运行
```bash
python src/main.py --config config/config.yaml
```

### 调试模式
```bash
python src/main.py --debug
```

### 查看系统状态
```bash
python src/main.py --status
```

## ⚙️ 配置

主配置文件位于 `config/config.yaml`，包含以下主要配置：

### 视觉配置
```yaml
vision:
  model_path: "yolov8n.pt"          # YOLO模型路径
  confidence_threshold: 0.5          # 置信度阈值
  input_size: [640, 480]            # 输入图像尺寸
  camera_id: 0                      # 摄像头ID
```

### 规划配置
```yaml
planning:
  priority_map:                     # 目标优先级映射
    0: 10  # 人
    1: 8   # 自行车
    2: 9   # 汽车
  max_concurrent_tasks: 2           # 最大并发任务数
  default_timeout: 30.0             # 默认超时时间
```

### 控制配置
```yaml
control:
  max_speed: 1.0                    # 最大速度
  acceleration: 0.5                 # 加速度
  position_tolerance: 0.01          # 位置容差
  safety_bounds:                    # 安全边界
    x: [-2.0, 2.0]
    y: [-2.0, 2.0]
    z: [0.0, 2.0]
```

### 环境变量支持
```bash
export ROBOT_VISION_MODEL_PATH="custom_model.pt"
export ROBOT_VISION_CONFIDENCE_THRESHOLD="0.7"
export ROBOT_VISION_MAX_SPEED="1.5"
```

## 🧪 测试

### 运行所有测试
```bash
python tests/run_tests.py
```

### 运行特定测试
```bash
python tests/run_tests.py --test test_object_detector.TestObjectDetector.test_detect
```

### 运行性能测试
```bash
python tests/run_tests.py --performance
```

### 保存测试结果
```bash
python tests/run_tests.py --save
```

## 📊 监控

系统提供完整的监控功能：

### 系统指标
- CPU使用率
- 内存使用率
- 磁盘使用率
- GPU使用率（如果可用）

### 性能指标
- 操作执行时间
- 成功率统计
- 错误率分析

### 告警机制
- 资源使用告警
- 性能异常告警
- 系统错误告警

## 🔧 开发

### 代码规范
项目遵循PEP 8代码规范，使用以下工具：

```bash
# 代码格式化
black src/ tests/

# 导入排序
isort src/ tests/

# 代码检查
flake8 src/ tests/

# 类型检查
mypy src/
```

### 添加新功能
1. 在相应模块中添加功能代码
2. 添加相应的测试用例
3. 更新配置文件（如需要）
4. 更新文档

### 调试技巧
1. 使用 `--debug` 参数启用详细日志
2. 查看 `logs/` 目录中的日志文件
3. 使用性能分析器定位性能问题
4. 利用监控系统观察系统状态

## 📈 性能优化

### 检测优化
- 使用批处理提高检测效率
- 调整输入图像尺寸平衡精度和速度
- 使用GPU加速（如果可用）

### 规划优化
- 合理设置规划范围
- 优化障碍物检测算法
- 使用缓存减少重复计算

### 控制优化
- 调整PID参数
- 优化控制频率
- 实现预测控制

## 🚨 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径
   - 确认网络连接（自动下载时）
   - 验证模型文件完整性

2. **摄像头无法打开**
   - 检查摄像头连接
   - 确认摄像头ID正确
   - 检查权限设置

3. **内存不足**
   - 减少批处理大小
   - 降低图像分辨率
   - 增加系统内存

4. **性能问题**
   - 启用GPU加速
   - 优化算法参数
   - 检查系统资源使用

### 日志分析
查看日志文件获取详细错误信息：
```bash
tail -f logs/robot_vision_*.log
```

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
- 创建 Issue
- 发送邮件至 [<EMAIL>]
- 查看文档和FAQ

## 🔄 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的视觉检测功能
- 任务和路径规划
- 机器人控制系统
- 监控和日志系统
- 完整测试覆盖