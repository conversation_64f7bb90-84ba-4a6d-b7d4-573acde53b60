"""
测试运行器
"""
import unittest
import sys
import os
import time
import json
from io import String<PERSON>
from typing import Dict, Any, List

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))


class TestResult:
    """测试结果类"""
    
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.error_tests = 0
        self.skipped_tests = 0
        self.execution_time = 0.0
        self.failures = []
        self.errors = []
        self.test_details = []
    
    def add_test_result(self, test_name: str, status: str, duration: float, 
                       error_msg: str = None):
        """添加测试结果"""
        self.test_details.append({
            'name': test_name,
            'status': status,
            'duration': duration,
            'error': error_msg
        })
        
        self.total_tests += 1
        if status == 'PASS':
            self.passed_tests += 1
        elif status == 'FAIL':
            self.failed_tests += 1
            if error_msg:
                self.failures.append(f"{test_name}: {error_msg}")
        elif status == 'ERROR':
            self.error_tests += 1
            if error_msg:
                self.errors.append(f"{test_name}: {error_msg}")
        elif status == 'SKIP':
            self.skipped_tests += 1
    
    def get_summary(self) -> Dict[str, Any]:
        """获取测试摘要"""
        return {
            'total': self.total_tests,
            'passed': self.passed_tests,
            'failed': self.failed_tests,
            'errors': self.error_tests,
            'skipped': self.skipped_tests,
            'success_rate': self.passed_tests / max(1, self.total_tests) * 100,
            'execution_time': self.execution_time
        }


class CustomTestResult(unittest.TestResult):
    """自定义测试结果收集器"""
    
    def __init__(self, test_result: TestResult):
        super().__init__()
        self.test_result = test_result
        self.current_test_start = None
    
    def startTest(self, test):
        """测试开始"""
        super().startTest(test)
        self.current_test_start = time.time()
    
    def addSuccess(self, test):
        """测试成功"""
        super().addSuccess(test)
        duration = time.time() - self.current_test_start
        self.test_result.add_test_result(
            str(test), 'PASS', duration
        )
    
    def addError(self, test, err):
        """测试错误"""
        super().addError(test, err)
        duration = time.time() - self.current_test_start
        error_msg = self._format_error(err)
        self.test_result.add_test_result(
            str(test), 'ERROR', duration, error_msg
        )
    
    def addFailure(self, test, err):
        """测试失败"""
        super().addFailure(test, err)
        duration = time.time() - self.current_test_start
        error_msg = self._format_error(err)
        self.test_result.add_test_result(
            str(test), 'FAIL', duration, error_msg
        )
    
    def addSkip(self, test, reason):
        """测试跳过"""
        super().addSkip(test, reason)
        duration = time.time() - self.current_test_start
        self.test_result.add_test_result(
            str(test), 'SKIP', duration, reason
        )
    
    def _format_error(self, err):
        """格式化错误信息"""
        return f"{err[0].__name__}: {str(err[1])}"


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.test_modules = [
            'test_object_detector',
            'test_path_planner',
            'test_task_planner'
        ]
        self.result = TestResult()
    
    def discover_tests(self) -> unittest.TestSuite:
        """发现测试"""
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        for module_name in self.test_modules:
            try:
                module = __import__(module_name)
                module_suite = loader.loadTestsFromModule(module)
                suite.addTest(module_suite)
                print(f"✓ Loaded tests from {module_name}")
            except ImportError as e:
                print(f"✗ Failed to load {module_name}: {e}")
            except Exception as e:
                print(f"✗ Error loading {module_name}: {e}")
        
        return suite
    
    def run_tests(self, verbosity: int = 2) -> TestResult:
        """运行测试"""
        print("=" * 70)
        print("ROBOT VISION SYSTEM - TEST SUITE")
        print("=" * 70)
        
        # 发现测试
        suite = self.discover_tests()
        
        if suite.countTestCases() == 0:
            print("No tests found!")
            return self.result
        
        print(f"Found {suite.countTestCases()} tests")
        print("-" * 70)
        
        # 运行测试
        start_time = time.time()
        
        # 创建自定义结果收集器
        custom_result = CustomTestResult(self.result)
        
        # 运行测试
        runner = unittest.TextTestRunner(
            stream=sys.stdout,
            verbosity=verbosity,
            resultclass=lambda: custom_result
        )
        
        runner.run(suite)
        
        self.result.execution_time = time.time() - start_time
        
        return self.result
    
    def print_summary(self):
        """打印测试摘要"""
        summary = self.result.get_summary()
        
        print("\n" + "=" * 70)
        print("TEST SUMMARY")
        print("=" * 70)
        
        print(f"Total Tests:     {summary['total']}")
        print(f"Passed:          {summary['passed']}")
        print(f"Failed:          {summary['failed']}")
        print(f"Errors:          {summary['errors']}")
        print(f"Skipped:         {summary['skipped']}")
        print(f"Success Rate:    {summary['success_rate']:.1f}%")
        print(f"Execution Time:  {summary['execution_time']:.2f}s")
        
        # 打印失败和错误详情
        if self.result.failures:
            print("\nFAILURES:")
            print("-" * 40)
            for failure in self.result.failures:
                print(f"  {failure}")
        
        if self.result.errors:
            print("\nERRORS:")
            print("-" * 40)
            for error in self.result.errors:
                print(f"  {error}")
        
        print("=" * 70)
    
    def save_results(self, filename: str = None):
        """保存测试结果"""
        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"test_results_{timestamp}.json"
        
        results_dir = os.path.join(os.path.dirname(__file__), 'results')
        os.makedirs(results_dir, exist_ok=True)
        
        filepath = os.path.join(results_dir, filename)
        
        data = {
            'summary': self.result.get_summary(),
            'test_details': self.result.test_details,
            'failures': self.result.failures,
            'errors': self.result.errors,
            'timestamp': time.time(),
            'date': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"Test results saved to: {filepath}")
    
    def run_specific_test(self, test_pattern: str):
        """运行特定测试"""
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromName(test_pattern)
        
        custom_result = CustomTestResult(self.result)
        runner = unittest.TextTestRunner(
            stream=sys.stdout,
            verbosity=2,
            resultclass=lambda: custom_result
        )
        
        start_time = time.time()
        runner.run(suite)
        self.result.execution_time = time.time() - start_time


def run_performance_tests():
    """运行性能测试"""
    print("\n" + "=" * 70)
    print("PERFORMANCE TESTS")
    print("=" * 70)
    
    # 这里可以添加性能测试
    # 例如：测试检测速度、内存使用等
    
    try:
        from test_object_detector import TestObjectDetectorPerformance
        
        suite = unittest.TestLoader().loadTestsFromTestCase(TestObjectDetectorPerformance)
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        if result.wasSuccessful():
            print("✓ All performance tests passed")
        else:
            print("✗ Some performance tests failed")
            
    except ImportError:
        print("Performance tests not available")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Robot Vision System Test Runner')
    parser.add_argument('--verbose', '-v', action='count', default=1,
                       help='Increase verbosity')
    parser.add_argument('--save', '-s', action='store_true',
                       help='Save test results to file')
    parser.add_argument('--performance', '-p', action='store_true',
                       help='Run performance tests')
    parser.add_argument('--test', '-t', type=str,
                       help='Run specific test (e.g., test_object_detector.TestObjectDetector.test_detect)')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.test:
        # 运行特定测试
        runner.run_specific_test(args.test)
    else:
        # 运行所有测试
        runner.run_tests(verbosity=args.verbose)
    
    # 打印摘要
    runner.print_summary()
    
    # 保存结果
    if args.save:
        runner.save_results()
    
    # 运行性能测试
    if args.performance:
        run_performance_tests()
    
    # 返回退出码
    summary = runner.result.get_summary()
    if summary['failed'] > 0 or summary['errors'] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == '__main__':
    main()
