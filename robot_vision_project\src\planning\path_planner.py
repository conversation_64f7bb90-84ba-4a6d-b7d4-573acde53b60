"""
路径规划器
"""
import numpy as np

class PathPlanner:
    def __init__(self):
        self.current_position = np.array([0, 0, 0])
        
    def plan_to_object(self, obj):
        """规划到目标的路径"""
        target_pos = self._bbox_to_position(obj['bbox'])
        
        # 简单直线路径规划
        path = self._generate_straight_path(
            self.current_position, 
            target_pos
        )
        
        return path
    
    def _bbox_to_position(self, bbox):
        """将边界框转换为3D位置"""
        x1, y1, x2, y2 = bbox
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2
        
        # 简化的深度估计
        depth = 1.0  # 假设深度为1米
        
        return np.array([center_x, center_y, depth])
    
    def _generate_straight_path(self, start, end):
        """生成直线路径"""
        steps = 10
        path = []
        for i in range(steps + 1):
            t = i / steps
            point = start + t * (end - start)
            path.append(point.tolist())
        return path