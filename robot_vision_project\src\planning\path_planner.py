"""
路径规划器
"""
import numpy as np
import heapq
import math
from typing import List, Tuple, Dict, Any, Optional
import time

from ..utils.logger import RobotVisionLogger, log_exceptions, log_performance
from ..utils.exceptions import (
    PathPlanningException, ValidationException,
    validate_not_none, validate_positive, validate_type
)


class PathPlanner:
    """路径规划器类"""

    def __init__(self, workspace_bounds: Optional[Tuple[float, float, float, float, float, float]] = None):
        """
        初始化路径规划器

        Args:
            workspace_bounds: 工作空间边界 (x_min, x_max, y_min, y_max, z_min, z_max)
        """
        self.logger = RobotVisionLogger().get_logger('PathPlanner')
        self.logger.info("Initializing PathPlanner")

        self.current_position = np.array([0.0, 0.0, 0.0])

        # 设置工作空间边界
        if workspace_bounds is None:
            self.workspace_bounds = (-5.0, 5.0, -5.0, 5.0, 0.0, 3.0)  # 默认边界
        else:
            self.workspace_bounds = workspace_bounds

        # 路径规划参数
        self.max_velocity = 1.0  # m/s
        self.max_acceleration = 0.5  # m/s²
        self.safety_margin = 0.1  # m

        # 障碍物列表
        self.obstacles = []

        self.logger.info("PathPlanner initialized successfully")

    @log_exceptions('PathPlanner')
    @log_performance('PathPlanner')
    def plan_to_object(self, obj: Dict[str, Any], algorithm: str = 'straight') -> List[List[float]]:
        """
        规划到目标的路径

        Args:
            obj: 目标对象，包含bbox信息
            algorithm: 路径规划算法 ('straight', 'astar', 'rrt')

        Returns:
            路径点列表
        """
        try:
            validate_not_none(obj, "obj")

            if 'bbox' not in obj:
                raise ValidationException("Object must contain 'bbox' field")

            target_pos = self._bbox_to_position(obj['bbox'])

            # 验证目标位置
            self._validate_position(target_pos)

            # 根据算法选择路径规划方法
            if algorithm == 'straight':
                path = self._generate_straight_path(self.current_position, target_pos)
            elif algorithm == 'astar':
                path = self._plan_astar(self.current_position, target_pos)
            elif algorithm == 'rrt':
                path = self._plan_rrt(self.current_position, target_pos)
            else:
                raise ValidationException(f"Unknown planning algorithm: {algorithm}")

            # 验证路径
            self._validate_path(path)

            self.logger.info(f"Path planned using {algorithm} algorithm with {len(path)} waypoints")
            return path

        except Exception as e:
            raise PathPlanningException(f"Path planning failed: {str(e)}")

    def _bbox_to_position(self, bbox: List[float]) -> np.ndarray:
        """将边界框转换为3D位置"""
        try:
            validate_not_none(bbox, "bbox")

            if len(bbox) != 4:
                raise ValidationException("Bounding box must have 4 elements [x1, y1, x2, y2]")

            x1, y1, x2, y2 = bbox
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2

            # 基于边界框大小的深度估计
            bbox_area = (x2 - x1) * (y2 - y1)
            depth = self._estimate_depth_from_bbox_size(bbox_area)

            # 转换像素坐标到世界坐标
            world_pos = self._pixel_to_world(center_x, center_y, depth)

            return world_pos

        except Exception as e:
            raise PathPlanningException(f"Bbox to position conversion failed: {str(e)}")

    def _estimate_depth_from_bbox_size(self, bbox_area: float) -> float:
        """基于边界框大小估计深度"""
        # 简化的深度估计模型
        # 假设已知物体的实际大小，根据图像中的大小估计距离
        reference_area = 10000  # 参考面积（像素²）
        reference_depth = 1.0   # 参考深度（米）

        if bbox_area <= 0:
            return reference_depth

        # 反比关系：面积越大，距离越近
        estimated_depth = reference_depth * math.sqrt(reference_area / bbox_area)

        # 限制深度范围
        return max(0.5, min(3.0, estimated_depth))

    def _pixel_to_world(self, pixel_x: float, pixel_y: float, depth: float) -> np.ndarray:
        """像素坐标转世界坐标"""
        # 简化的相机模型
        # 假设相机内参已知
        fx, fy = 500.0, 500.0  # 焦距
        cx, cy = 320.0, 240.0  # 主点

        # 像素坐标转相机坐标
        x_cam = (pixel_x - cx) * depth / fx
        y_cam = (pixel_y - cy) * depth / fy
        z_cam = depth

        # 相机坐标转世界坐标（假设相机在原点，朝向+Z）
        world_pos = np.array([x_cam, y_cam, z_cam])

        return world_pos

    def _generate_straight_path(self, start: np.ndarray, end: np.ndarray,
                              num_points: int = 10) -> List[List[float]]:
        """生成直线路径"""
        try:
            validate_positive(num_points, "num_points")

            path = []
            for i in range(num_points + 1):
                t = i / num_points
                point = start + t * (end - start)
                path.append(point.tolist())

            return path

        except Exception as e:
            raise PathPlanningException(f"Straight path generation failed: {str(e)}")

    def _plan_astar(self, start: np.ndarray, goal: np.ndarray) -> List[List[float]]:
        """A*路径规划算法"""
        try:
            # 简化的A*实现
            # 在实际应用中，需要更复杂的网格化和启发式函数

            # 如果没有障碍物，直接返回直线路径
            if not self.obstacles:
                return self._generate_straight_path(start, goal)

            # 创建简单的网格
            grid_size = 0.1  # 网格分辨率
            path = self._astar_grid_search(start, goal, grid_size)

            return path

        except Exception as e:
            raise PathPlanningException(f"A* planning failed: {str(e)}")

    def _plan_rrt(self, start: np.ndarray, goal: np.ndarray,
                  max_iterations: int = 1000) -> List[List[float]]:
        """RRT路径规划算法"""
        try:
            # 简化的RRT实现
            if not self.obstacles:
                return self._generate_straight_path(start, goal)

            # RRT树节点
            tree = [start.copy()]
            parent = [-1]  # 父节点索引

            step_size = 0.2  # 步长

            for _ in range(max_iterations):
                # 随机采样
                if np.random.random() < 0.1:  # 10%概率采样目标点
                    rand_point = goal.copy()
                else:
                    rand_point = self._sample_random_point()

                # 找到最近节点
                nearest_idx = self._find_nearest_node(tree, rand_point)
                nearest_point = tree[nearest_idx]

                # 扩展新节点
                direction = rand_point - nearest_point
                distance = np.linalg.norm(direction)

                if distance > step_size:
                    direction = direction / distance * step_size

                new_point = nearest_point + direction

                # 检查碰撞
                if not self._check_collision(nearest_point, new_point):
                    tree.append(new_point)
                    parent.append(nearest_idx)

                    # 检查是否到达目标
                    if np.linalg.norm(new_point - goal) < step_size:
                        # 构建路径
                        path = self._extract_rrt_path(tree, parent, len(tree) - 1)
                        return path

            # 如果RRT失败，返回直线路径
            self.logger.warning("RRT failed to find path, using straight line")
            return self._generate_straight_path(start, goal)

        except Exception as e:
            raise PathPlanningException(f"RRT planning failed: {str(e)}")

    def _astar_grid_search(self, start: np.ndarray, goal: np.ndarray,
                          grid_size: float) -> List[List[float]]:
        """A*网格搜索实现"""
        # 简化实现，实际应用中需要更复杂的逻辑
        return self._generate_straight_path(start, goal)

    def _sample_random_point(self) -> np.ndarray:
        """在工作空间内随机采样点"""
        x_min, x_max, y_min, y_max, z_min, z_max = self.workspace_bounds

        x = np.random.uniform(x_min, x_max)
        y = np.random.uniform(y_min, y_max)
        z = np.random.uniform(z_min, z_max)

        return np.array([x, y, z])

    def _find_nearest_node(self, tree: List[np.ndarray], point: np.ndarray) -> int:
        """找到树中最近的节点"""
        min_dist = float('inf')
        nearest_idx = 0

        for i, node in enumerate(tree):
            dist = np.linalg.norm(node - point)
            if dist < min_dist:
                min_dist = dist
                nearest_idx = i

        return nearest_idx

    def _check_collision(self, start: np.ndarray, end: np.ndarray) -> bool:
        """检查路径段是否与障碍物碰撞"""
        # 简化的碰撞检测
        for obstacle in self.obstacles:
            if self._line_intersects_obstacle(start, end, obstacle):
                return True
        return False

    def _line_intersects_obstacle(self, start: np.ndarray, end: np.ndarray,
                                 obstacle: Dict[str, Any]) -> bool:
        """检查线段是否与障碍物相交"""
        # 简化实现，假设障碍物是球形
        center = np.array(obstacle.get('center', [0, 0, 0]))
        radius = obstacle.get('radius', 0.1)

        # 点到线段的距离
        line_vec = end - start
        point_vec = center - start

        if np.linalg.norm(line_vec) == 0:
            return np.linalg.norm(point_vec) <= radius

        t = max(0, min(1, np.dot(point_vec, line_vec) / np.dot(line_vec, line_vec)))
        closest_point = start + t * line_vec
        distance = np.linalg.norm(center - closest_point)

        return distance <= radius + self.safety_margin

    def _extract_rrt_path(self, tree: List[np.ndarray], parent: List[int],
                         goal_idx: int) -> List[List[float]]:
        """从RRT树中提取路径"""
        path = []
        current_idx = goal_idx

        while current_idx != -1:
            path.append(tree[current_idx].tolist())
            current_idx = parent[current_idx]

        path.reverse()
        return path

    def _validate_position(self, position: np.ndarray):
        """验证位置是否在工作空间内"""
        x_min, x_max, y_min, y_max, z_min, z_max = self.workspace_bounds

        x, y, z = position

        if not (x_min <= x <= x_max):
            raise ValidationException(f"X coordinate {x} out of bounds [{x_min}, {x_max}]")

        if not (y_min <= y <= y_max):
            raise ValidationException(f"Y coordinate {y} out of bounds [{y_min}, {y_max}]")

        if not (z_min <= z <= z_max):
            raise ValidationException(f"Z coordinate {z} out of bounds [{z_min}, {z_max}]")

    def _validate_path(self, path: List[List[float]]):
        """验证路径有效性"""
        validate_not_none(path, "path")

        if not path:
            raise ValidationException("Path cannot be empty")

        for i, point in enumerate(path):
            if len(point) != 3:
                raise ValidationException(f"Path point {i} must have 3 coordinates")

            try:
                self._validate_position(np.array(point))
            except ValidationException as e:
                raise ValidationException(f"Path point {i} invalid: {str(e)}")

    @log_exceptions('PathPlanner')
    def add_obstacle(self, center: List[float], radius: float):
        """添加球形障碍物"""
        validate_not_none(center, "center")
        validate_positive(radius, "radius")

        if len(center) != 3:
            raise ValidationException("Obstacle center must have 3 coordinates")

        obstacle = {
            'center': center,
            'radius': radius,
            'type': 'sphere'
        }

        self.obstacles.append(obstacle)
        self.logger.info(f"Added obstacle at {center} with radius {radius}")

    @log_exceptions('PathPlanner')
    def clear_obstacles(self):
        """清除所有障碍物"""
        self.obstacles.clear()
        self.logger.info("All obstacles cleared")

    @log_exceptions('PathPlanner')
    def update_current_position(self, position: List[float]):
        """更新当前位置"""
        validate_not_none(position, "position")

        if len(position) != 3:
            raise ValidationException("Position must have 3 coordinates")

        new_pos = np.array(position)
        self._validate_position(new_pos)

        old_pos = self.current_position.copy()
        self.current_position = new_pos

        self.logger.debug(f"Position updated from {old_pos} to {new_pos}")

    def get_path_length(self, path: List[List[float]]) -> float:
        """计算路径长度"""
        if not path or len(path) < 2:
            return 0.0

        total_length = 0.0
        for i in range(1, len(path)):
            p1 = np.array(path[i-1])
            p2 = np.array(path[i])
            total_length += np.linalg.norm(p2 - p1)

        return total_length

    def smooth_path(self, path: List[List[float]], smoothing_factor: float = 0.5) -> List[List[float]]:
        """路径平滑"""
        if len(path) < 3:
            return path

        smoothed_path = [path[0]]  # 保持起点

        for i in range(1, len(path) - 1):
            prev_point = np.array(path[i-1])
            curr_point = np.array(path[i])
            next_point = np.array(path[i+1])

            # 简单的平均平滑
            smoothed_point = (1 - smoothing_factor) * curr_point + \
                           smoothing_factor * 0.5 * (prev_point + next_point)

            smoothed_path.append(smoothed_point.tolist())

        smoothed_path.append(path[-1])  # 保持终点

        return smoothed_path