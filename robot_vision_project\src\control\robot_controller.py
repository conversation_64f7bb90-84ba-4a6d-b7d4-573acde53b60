"""
机器人控制器
"""
import time

class RobotController:
    def __init__(self, config):
        self.max_speed = config['max_speed']
        self.current_position = [0, 0, 0]
        
    def execute(self, plan):
        """执行任务计划"""
        if not plan:
            return
            
        print(f"执行 {plan['total_tasks']} 个任务")
        
        for i, task in enumerate(plan['tasks']):
            print(f"执行任务 {i+1}: {task['type']}")
            self._execute_task(task)
            
    def _execute_task(self, task):
        """执行单个任务"""
        if task['type'] == 'approach_and_grasp':
            self._follow_path(task['path'])
            self._grasp_object(task['target'])
            
    def _follow_path(self, path):
        """沿路径移动"""
        for point in path:
            print(f"移动到位置: {point}")
            self.current_position = point
            time.sleep(0.1)  # 模拟移动时间
            
    def _grasp_object(self, target):
        """抓取目标"""
        print(f"抓取目标: 类别{target['class']}, 置信度{target['confidence']:.2f}")
        time.sleep(0.5)  # 模拟抓取时间