"""
目标检测模块
"""
import cv2
import numpy as np
import os
import time
from typing import List, Dict, Any, Optional, Union
from ultralytics import YOLO

from ..utils.logger import RobotVisionLogger, log_exceptions, log_performance
from ..utils.exceptions import (
    ModelLoadException, DetectionException, CameraException,
    ValidationException, ImageProcessingException, validate_not_none,
    validate_positive, validate_type
)


class ObjectDetector:
    """目标检测器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化目标检测器

        Args:
            config: 配置字典，包含model_path和confidence_threshold
        """
        self.logger = RobotVisionLogger().get_logger('ObjectDetector')
        self.logger.info("Initializing ObjectDetector")

        try:
            # 验证配置
            self._validate_config(config)

            # 设置参数
            self.model_path = config['model_path']
            self.confidence_threshold = config['confidence_threshold']
            self.input_size = config.get('input_size', [640, 480])

            # 加载模型
            self.model = self._load_model()

            # 初始化摄像头相关参数
            self.camera_id = config.get('camera_id', 0)
            self.camera_timeout = config.get('camera_timeout', 5.0)

            self.logger.info("ObjectDetector initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize ObjectDetector: {str(e)}")
            raise ModelLoadException(f"ObjectDetector initialization failed: {str(e)}")

    def _validate_config(self, config: Dict[str, Any]):
        """验证配置参数"""
        validate_not_none(config, "config")
        validate_type(config, dict, "config")

        if 'model_path' not in config:
            raise ValidationException("model_path is required in config")

        if 'confidence_threshold' not in config:
            raise ValidationException("confidence_threshold is required in config")

        validate_positive(config['confidence_threshold'], "confidence_threshold")

        if config['confidence_threshold'] > 1.0:
            raise ValidationException("confidence_threshold must be <= 1.0")

    def _load_model(self) -> YOLO:
        """加载YOLO模型"""
        try:
            if not os.path.exists(self.model_path):
                raise ModelLoadException(f"Model file not found: {self.model_path}")

            self.logger.info(f"Loading model from {self.model_path}")
            model = YOLO(self.model_path)

            # 验证模型
            if model is None:
                raise ModelLoadException("Failed to load YOLO model")

            self.logger.info("Model loaded successfully")
            return model

        except Exception as e:
            raise ModelLoadException(f"Failed to load model: {str(e)}")

    @log_exceptions('ObjectDetector')
    @log_performance('ObjectDetector')
    def detect(self, image: Optional[np.ndarray] = None) -> List[Dict[str, Any]]:
        """
        检测图像中的目标

        Args:
            image: 输入图像，如果为None则从摄像头获取

        Returns:
            检测到的目标列表
        """
        try:
            if image is None:
                image = self._capture_image()

            # 验证图像
            self._validate_image(image)

            # 预处理图像
            processed_image = self._preprocess_image(image)

            # 执行检测
            results = self.model(processed_image)

            # 解析结果
            objects = self._parse_results(results)

            self.logger.info(f"Detected {len(objects)} objects")
            return objects

        except Exception as e:
            self.logger.error(f"Detection failed: {str(e)}")
            raise DetectionException(f"Object detection failed: {str(e)}")

    def _validate_image(self, image: np.ndarray):
        """验证图像格式"""
        validate_not_none(image, "image")

        if not isinstance(image, np.ndarray):
            raise ImageProcessingException("Image must be a numpy array")

        if len(image.shape) != 3:
            raise ImageProcessingException("Image must be 3-dimensional (H, W, C)")

        if image.shape[2] != 3:
            raise ImageProcessingException("Image must have 3 channels (BGR)")

        if image.size == 0:
            raise ImageProcessingException("Image is empty")

    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """预处理图像"""
        try:
            # 调整大小
            if image.shape[:2] != tuple(reversed(self.input_size)):
                image = cv2.resize(image, tuple(self.input_size))

            return image

        except Exception as e:
            raise ImageProcessingException(f"Image preprocessing failed: {str(e)}")

    @log_exceptions('ObjectDetector')
    def _capture_image(self) -> np.ndarray:
        """从摄像头捕获图像"""
        cap = None
        try:
            self.logger.debug(f"Capturing image from camera {self.camera_id}")

            cap = cv2.VideoCapture(self.camera_id)
            if not cap.isOpened():
                raise CameraException(f"Cannot open camera {self.camera_id}")

            # 设置超时
            start_time = time.time()
            ret, frame = cap.read()

            if time.time() - start_time > self.camera_timeout:
                raise CameraException("Camera capture timeout")

            if not ret or frame is None:
                raise CameraException("Failed to capture frame from camera")

            self.logger.debug("Image captured successfully")
            return frame

        except Exception as e:
            raise CameraException(f"Camera capture failed: {str(e)}")
        finally:
            if cap is not None:
                cap.release()

    def _parse_results(self, results) -> List[Dict[str, Any]]:
        """解析检测结果"""
        try:
            objects = []

            if not results:
                return objects

            for result in results:
                if result.boxes is None:
                    continue

                for box in result.boxes:
                    if box.conf is None or box.cls is None or box.xyxy is None:
                        continue

                    confidence = float(box.conf)
                    if confidence > self.confidence_threshold:
                        objects.append({
                            'class': int(box.cls),
                            'confidence': confidence,
                            'bbox': box.xyxy.tolist()[0],
                            'timestamp': time.time()
                        })

            return objects

        except Exception as e:
            raise DetectionException(f"Failed to parse detection results: {str(e)}")

    @log_exceptions('ObjectDetector')
    def detect_batch(self, images: List[np.ndarray]) -> List[List[Dict[str, Any]]]:
        """
        批量检测多张图像

        Args:
            images: 图像列表

        Returns:
            每张图像的检测结果列表
        """
        validate_not_none(images, "images")
        validate_type(images, list, "images")

        if not images:
            return []

        results = []
        for i, image in enumerate(images):
            try:
                objects = self.detect(image)
                results.append(objects)
            except Exception as e:
                self.logger.error(f"Failed to detect objects in image {i}: {str(e)}")
                results.append([])

        return results

    def set_confidence_threshold(self, threshold: float):
        """设置置信度阈值"""
        validate_positive(threshold, "threshold")
        if threshold > 1.0:
            raise ValidationException("Confidence threshold must be <= 1.0")

        old_threshold = self.confidence_threshold
        self.confidence_threshold = threshold
        self.logger.info(f"Confidence threshold changed from {old_threshold} to {threshold}")

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        try:
            return {
                'model_path': self.model_path,
                'confidence_threshold': self.confidence_threshold,
                'input_size': self.input_size,
                'model_type': 'YOLO',
                'camera_id': self.camera_id
            }
        except Exception as e:
            self.logger.error(f"Failed to get model info: {str(e)}")
            return {}

    def filter_objects_by_class(self, objects: List[Dict[str, Any]],
                               target_classes: List[int]) -> List[Dict[str, Any]]:
        """根据类别过滤检测结果"""
        validate_not_none(objects, "objects")
        validate_not_none(target_classes, "target_classes")

        filtered = [obj for obj in objects if obj['class'] in target_classes]
        self.logger.debug(f"Filtered {len(objects)} objects to {len(filtered)} objects")
        return filtered

    def filter_objects_by_confidence(self, objects: List[Dict[str, Any]],
                                   min_confidence: float) -> List[Dict[str, Any]]:
        """根据置信度过滤检测结果"""
        validate_not_none(objects, "objects")
        validate_positive(min_confidence, "min_confidence")

        filtered = [obj for obj in objects if obj['confidence'] >= min_confidence]
        self.logger.debug(f"Filtered {len(objects)} objects to {len(filtered)} objects")
        return filtered

    def get_detection_statistics(self, objects: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取检测统计信息"""
        if not objects:
            return {'total_objects': 0, 'classes': {}, 'avg_confidence': 0.0}

        class_counts = {}
        total_confidence = 0.0

        for obj in objects:
            class_id = obj['class']
            class_counts[class_id] = class_counts.get(class_id, 0) + 1
            total_confidence += obj['confidence']

        return {
            'total_objects': len(objects),
            'classes': class_counts,
            'avg_confidence': total_confidence / len(objects),
            'max_confidence': max(obj['confidence'] for obj in objects),
            'min_confidence': min(obj['confidence'] for obj in objects)
        }

    def __del__(self):
        """析构函数"""
        try:
            self.logger.info("ObjectDetector destroyed")
        except:
            pass