"""
目标检测模块
"""
import cv2
import numpy as np
from ultralytics import YOLO

class ObjectDetector:
    def __init__(self, config):
        self.model = YOLO(config['model_path'])
        self.confidence_threshold = config['confidence_threshold']
        
    def detect(self, image=None):
        """检测图像中的目标"""
        if image is None:
            # 从摄像头获取图像
            image = self._capture_image()
            
        results = self.model(image)
        objects = self._parse_results(results)
        return objects
    
    def _capture_image(self):
        """从摄像头捕获图像"""
        cap = cv2.VideoCapture(0)
        ret, frame = cap.read()
        cap.release()
        return frame
    
    def _parse_results(self, results):
        """解析检测结果"""
        objects = []
        for result in results:
            for box in result.boxes:
                if box.conf > self.confidence_threshold:
                    objects.append({
                        'class': int(box.cls),
                        'confidence': float(box.conf),
                        'bbox': box.xyxy.tolist()[0]
                    })
        return objects