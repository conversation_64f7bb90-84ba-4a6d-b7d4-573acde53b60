"""
任务规划器
"""
import numpy as np
import time
import uuid
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

from .path_planner import PathPlanner
from ..utils.logger import RobotVisionLogger, log_exceptions, log_performance
from ..utils.exceptions import (
    TaskPlanningException, ValidationException,
    validate_not_none, validate_type
)


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(Enum):
    """任务类型枚举"""
    APPROACH_AND_GRASP = "approach_and_grasp"
    INSPECT = "inspect"
    MOVE_TO_POSITION = "move_to_position"
    WAIT = "wait"
    CUSTOM = "custom"


class Task:
    """任务类"""

    def __init__(self, task_type: TaskType, target: Dict[str, Any],
                 priority: float = 1.0, timeout: float = 30.0):
        self.id = str(uuid.uuid4())
        self.type = task_type
        self.target = target
        self.priority = priority
        self.timeout = timeout
        self.status = TaskStatus.PENDING
        self.created_time = time.time()
        self.start_time = None
        self.end_time = None
        self.path = None
        self.error_message = None
        self.retry_count = 0
        self.max_retries = 3

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'type': self.type.value,
            'target': self.target,
            'priority': self.priority,
            'timeout': self.timeout,
            'status': self.status.value,
            'created_time': self.created_time,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'path': self.path,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries
        }


class TaskPlanner:
    """任务规划器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化任务规划器

        Args:
            config: 配置字典
        """
        self.logger = RobotVisionLogger().get_logger('TaskPlanner')
        self.logger.info("Initializing TaskPlanner")

        try:
            # 验证配置
            self._validate_config(config)

            # 初始化路径规划器
            self.path_planner = PathPlanner()

            # 设置优先级映射
            self.priority_map = config.get('priority_map', {})

            # 任务队列
            self.task_queue = []
            self.completed_tasks = []
            self.failed_tasks = []

            # 规划参数
            self.max_concurrent_tasks = config.get('max_concurrent_tasks', 1)
            self.default_timeout = config.get('default_timeout', 30.0)
            self.planning_horizon = config.get('planning_horizon', 10)

            self.logger.info("TaskPlanner initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize TaskPlanner: {str(e)}")
            raise TaskPlanningException(f"TaskPlanner initialization failed: {str(e)}")

    def _validate_config(self, config: Dict[str, Any]):
        """验证配置参数"""
        validate_not_none(config, "config")
        validate_type(config, dict, "config")

    @log_exceptions('TaskPlanner')
    @log_performance('TaskPlanner')
    def plan(self, detected_objects: List[Dict[str, Any]],
             context: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        根据检测到的目标制定任务计划

        Args:
            detected_objects: 检测到的目标列表
            context: 规划上下文信息

        Returns:
            任务计划字典
        """
        try:
            if not detected_objects:
                self.logger.info("No objects detected, returning empty plan")
                return None

            self.logger.info(f"Planning for {len(detected_objects)} detected objects")

            # 清理过期任务
            self._cleanup_expired_tasks()

            # 按优先级排序目标
            sorted_objects = self._prioritize_objects(detected_objects, context)

            # 生成任务序列
            new_tasks = []
            for obj in sorted_objects[:self.planning_horizon]:
                task = self._create_task(obj, context)
                if task:
                    new_tasks.append(task)

            # 更新任务队列
            self._update_task_queue(new_tasks)

            # 生成执行计划
            execution_plan = self._generate_execution_plan()

            self.logger.info(f"Generated plan with {len(execution_plan['tasks'])} tasks")
            return execution_plan

        except Exception as e:
            self.logger.error(f"Task planning failed: {str(e)}")
            raise TaskPlanningException(f"Task planning failed: {str(e)}")

    def _prioritize_objects(self, objects: List[Dict[str, Any]],
                           context: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """按优先级对目标排序"""
        try:
            def calculate_priority(obj):
                # 基础优先级
                base_priority = self.priority_map.get(obj['class'], 1.0)

                # 置信度加权
                confidence_weight = obj.get('confidence', 0.5)

                # 距离加权（假设距离信息可用）
                distance_weight = 1.0
                if 'distance' in obj:
                    distance_weight = 1.0 / max(0.1, obj['distance'])

                # 时间衰减（新检测的目标优先级更高）
                time_weight = 1.0
                if 'timestamp' in obj:
                    age = time.time() - obj['timestamp']
                    time_weight = max(0.1, 1.0 - age / 60.0)  # 1分钟内衰减

                # 上下文相关优先级调整
                context_weight = 1.0
                if context and 'urgent_classes' in context:
                    if obj['class'] in context['urgent_classes']:
                        context_weight = 2.0

                total_priority = (base_priority * confidence_weight *
                                distance_weight * time_weight * context_weight)

                return total_priority

            sorted_objects = sorted(objects, key=calculate_priority, reverse=True)

            self.logger.debug(f"Prioritized {len(objects)} objects")
            return sorted_objects

        except Exception as e:
            self.logger.error(f"Object prioritization failed: {str(e)}")
            return objects  # 返回原始列表作为后备

    def _create_task(self, obj: Dict[str, Any],
                    context: Optional[Dict[str, Any]] = None) -> Optional[Task]:
        """为目标创建任务"""
        try:
            # 确定任务类型
            task_type = self._determine_task_type(obj, context)

            # 计算优先级
            priority = self.priority_map.get(obj['class'], 1.0)

            # 创建任务
            task = Task(
                task_type=task_type,
                target=obj,
                priority=priority,
                timeout=self.default_timeout
            )

            # 规划路径
            if task_type in [TaskType.APPROACH_AND_GRASP, TaskType.INSPECT]:
                try:
                    task.path = self.path_planner.plan_to_object(obj)
                except Exception as e:
                    self.logger.warning(f"Path planning failed for task {task.id}: {str(e)}")
                    task.path = []

            self.logger.debug(f"Created task {task.id} for object class {obj['class']}")
            return task

        except Exception as e:
            self.logger.error(f"Task creation failed: {str(e)}")
            return None

    def _determine_task_type(self, obj: Dict[str, Any],
                           context: Optional[Dict[str, Any]] = None) -> TaskType:
        """确定任务类型"""
        # 简化的任务类型确定逻辑
        # 实际应用中可能需要更复杂的决策逻辑

        if context and 'task_preferences' in context:
            preferences = context['task_preferences']
            if obj['class'] in preferences:
                task_type_str = preferences[obj['class']]
                try:
                    return TaskType(task_type_str)
                except ValueError:
                    pass

        # 默认任务类型
        return TaskType.APPROACH_AND_GRASP

    def _update_task_queue(self, new_tasks: List[Task]):
        """更新任务队列"""
        # 移除重复任务（基于目标相似性）
        filtered_tasks = []
        for new_task in new_tasks:
            if not self._is_duplicate_task(new_task):
                filtered_tasks.append(new_task)

        # 添加到队列
        self.task_queue.extend(filtered_tasks)

        # 按优先级排序
        self.task_queue.sort(key=lambda t: t.priority, reverse=True)

        self.logger.debug(f"Task queue updated, now contains {len(self.task_queue)} tasks")

    def _is_duplicate_task(self, new_task: Task) -> bool:
        """检查是否为重复任务"""
        for existing_task in self.task_queue:
            if (existing_task.type == new_task.type and
                existing_task.status == TaskStatus.PENDING and
                self._are_targets_similar(existing_task.target, new_task.target)):
                return True
        return False

    def _are_targets_similar(self, target1: Dict[str, Any], target2: Dict[str, Any]) -> bool:
        """判断两个目标是否相似"""
        # 简化的相似性判断
        if target1.get('class') != target2.get('class'):
            return False

        # 检查边界框重叠
        bbox1 = target1.get('bbox', [])
        bbox2 = target2.get('bbox', [])

        if len(bbox1) == 4 and len(bbox2) == 4:
            overlap = self._calculate_bbox_overlap(bbox1, bbox2)
            return overlap > 0.5  # 50%重叠认为是同一目标

        return False

    def _calculate_bbox_overlap(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算边界框重叠率"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2

        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)

        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0

        intersection = (x2_i - x1_i) * (y2_i - y1_i)

        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def _generate_execution_plan(self) -> Dict[str, Any]:
        """生成执行计划"""
        # 选择要执行的任务
        executable_tasks = []
        for task in self.task_queue[:self.max_concurrent_tasks]:
            if task.status == TaskStatus.PENDING:
                executable_tasks.append(task)

        # 转换为字典格式
        task_dicts = [task.to_dict() for task in executable_tasks]

        return {
            'tasks': task_dicts,
            'total_tasks': len(task_dicts),
            'queue_size': len(self.task_queue),
            'timestamp': time.time()
        }

    def _cleanup_expired_tasks(self):
        """清理过期任务"""
        current_time = time.time()
        expired_tasks = []

        for task in self.task_queue[:]:
            if (task.status == TaskStatus.IN_PROGRESS and
                task.start_time and
                current_time - task.start_time > task.timeout):

                task.status = TaskStatus.FAILED
                task.error_message = "Task timeout"
                task.end_time = current_time

                expired_tasks.append(task)
                self.task_queue.remove(task)
                self.failed_tasks.append(task)

        if expired_tasks:
            self.logger.warning(f"Cleaned up {len(expired_tasks)} expired tasks")

    @log_exceptions('TaskPlanner')
    def update_task_status(self, task_id: str, status: TaskStatus,
                          error_message: Optional[str] = None):
        """更新任务状态"""
        task = self._find_task_by_id(task_id)
        if not task:
            raise ValidationException(f"Task {task_id} not found")

        old_status = task.status
        task.status = status

        current_time = time.time()

        if status == TaskStatus.IN_PROGRESS and old_status == TaskStatus.PENDING:
            task.start_time = current_time
        elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            task.end_time = current_time

            # 移动到相应的列表
            if task in self.task_queue:
                self.task_queue.remove(task)

            if status == TaskStatus.COMPLETED:
                self.completed_tasks.append(task)
            elif status == TaskStatus.FAILED:
                task.error_message = error_message
                self.failed_tasks.append(task)

        self.logger.info(f"Task {task_id} status updated from {old_status.value} to {status.value}")

    def _find_task_by_id(self, task_id: str) -> Optional[Task]:
        """根据ID查找任务"""
        for task in self.task_queue + self.completed_tasks + self.failed_tasks:
            if task.id == task_id:
                return task
        return None

    @log_exceptions('TaskPlanner')
    def retry_failed_task(self, task_id: str) -> bool:
        """重试失败的任务"""
        task = self._find_task_by_id(task_id)
        if not task or task.status != TaskStatus.FAILED:
            return False

        if task.retry_count >= task.max_retries:
            self.logger.warning(f"Task {task_id} has exceeded max retries")
            return False

        # 重置任务状态
        task.status = TaskStatus.PENDING
        task.start_time = None
        task.end_time = None
        task.retry_count += 1
        task.error_message = None

        # 移回任务队列
        if task in self.failed_tasks:
            self.failed_tasks.remove(task)
        self.task_queue.append(task)

        # 重新排序
        self.task_queue.sort(key=lambda t: t.priority, reverse=True)

        self.logger.info(f"Task {task_id} queued for retry (attempt {task.retry_count})")
        return True

    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        return {
            'pending_tasks': len([t for t in self.task_queue if t.status == TaskStatus.PENDING]),
            'in_progress_tasks': len([t for t in self.task_queue if t.status == TaskStatus.IN_PROGRESS]),
            'completed_tasks': len(self.completed_tasks),
            'failed_tasks': len(self.failed_tasks),
            'total_tasks': len(self.task_queue) + len(self.completed_tasks) + len(self.failed_tasks),
            'queue_size': len(self.task_queue)
        }

    def clear_completed_tasks(self):
        """清理已完成的任务"""
        count = len(self.completed_tasks)
        self.completed_tasks.clear()
        self.logger.info(f"Cleared {count} completed tasks")

    def get_task_by_id(self, task_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取任务信息"""
        task = self._find_task_by_id(task_id)
        return task.to_dict() if task else None