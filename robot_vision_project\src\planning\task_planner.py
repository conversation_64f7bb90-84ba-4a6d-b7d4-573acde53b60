"""
任务规划器
"""
import numpy as np
from .path_planner import PathPlanner

class TaskPlanner:
    def __init__(self, config):
        self.path_planner = PathPlanner()
        self.priority_map = config['priority_map']
        
    def plan(self, detected_objects):
        """根据检测到的目标制定任务计划"""
        if not detected_objects:
            return None
            
        # 按优先级排序目标
        sorted_objects = self._prioritize_objects(detected_objects)
        
        # 生成任务序列
        tasks = []
        for obj in sorted_objects:
            task = self._create_task(obj)
            tasks.append(task)
            
        return {
            'tasks': tasks,
            'total_tasks': len(tasks)
        }
    
    def _prioritize_objects(self, objects):
        """按优先级对目标排序"""
        return sorted(objects, 
                     key=lambda x: self.priority_map.get(x['class'], 0), 
                     reverse=True)
    
    def _create_task(self, obj):
        """为目标创建任务"""
        return {
            'type': 'approach_and_grasp',
            'target': obj,
            'path': self.path_planner.plan_to_object(obj)
        }