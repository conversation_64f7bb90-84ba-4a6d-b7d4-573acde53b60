"""
任务规划器测试
"""
import unittest
import time
import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.planning.task_planner import TaskPlanner, Task, TaskStatus, TaskType
from src.utils.exceptions import TaskPlanningException, ValidationException


class TestTask(unittest.TestCase):
    """任务类测试"""
    
    def test_task_creation(self):
        """测试任务创建"""
        target = {'class': 0, 'confidence': 0.8, 'bbox': [100, 100, 200, 200]}
        task = Task(TaskType.APPROACH_AND_GRASP, target, priority=5.0)
        
        self.assertEqual(task.type, TaskType.APPROACH_AND_GRASP)
        self.assertEqual(task.target, target)
        self.assertEqual(task.priority, 5.0)
        self.assertEqual(task.status, TaskStatus.PENDING)
        self.assertIsNotNone(task.id)
        self.assertEqual(task.retry_count, 0)
    
    def test_task_to_dict(self):
        """测试任务转字典"""
        target = {'class': 0, 'confidence': 0.8}
        task = Task(TaskType.INSPECT, target)
        
        task_dict = task.to_dict()
        
        self.assertEqual(task_dict['type'], 'inspect')
        self.assertEqual(task_dict['target'], target)
        self.assertEqual(task_dict['status'], 'pending')
        self.assertIn('id', task_dict)
        self.assertIn('created_time', task_dict)


class TestTaskPlanner(unittest.TestCase):
    """任务规划器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = {
            'priority_map': {
                0: 10,  # 人
                1: 8,   # 自行车
                2: 9    # 汽车
            },
            'max_concurrent_tasks': 2,
            'default_timeout': 30.0,
            'planning_horizon': 5
        }
        
        self.planner = TaskPlanner(self.config)
        
        # 测试检测对象
        self.detected_objects = [
            {
                'class': 0,
                'confidence': 0.9,
                'bbox': [100, 100, 200, 200],
                'timestamp': time.time()
            },
            {
                'class': 1,
                'confidence': 0.7,
                'bbox': [300, 300, 400, 400],
                'timestamp': time.time()
            },
            {
                'class': 2,
                'confidence': 0.8,
                'bbox': [500, 500, 600, 600],
                'timestamp': time.time()
            }
        ]
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.planner.max_concurrent_tasks, 2)
        self.assertEqual(self.planner.default_timeout, 30.0)
        self.assertEqual(len(self.planner.task_queue), 0)
        self.assertIsNotNone(self.planner.path_planner)
    
    def test_init_invalid_config(self):
        """测试无效配置初始化"""
        with self.assertRaises(ValidationException):
            TaskPlanner(None)
    
    def test_plan_with_objects(self):
        """测试有目标的规划"""
        plan = self.planner.plan(self.detected_objects)
        
        self.assertIsNotNone(plan)
        self.assertIn('tasks', plan)
        self.assertIn('total_tasks', plan)
        self.assertGreater(plan['total_tasks'], 0)
        
        # 检查任务优先级排序
        tasks = plan['tasks']
        if len(tasks) > 1:
            # 人(class=0, priority=10)应该排在自行车(class=1, priority=8)前面
            class_0_found = False
            class_1_found = False
            for task in tasks:
                if task['target']['class'] == 0:
                    class_0_found = True
                elif task['target']['class'] == 1:
                    class_1_found = True
                    if class_0_found:
                        break  # 正确顺序
                    else:
                        self.fail("Priority ordering incorrect")
    
    def test_plan_empty_objects(self):
        """测试空目标列表规划"""
        plan = self.planner.plan([])
        self.assertIsNone(plan)
    
    def test_plan_none_objects(self):
        """测试None目标规划"""
        plan = self.planner.plan(None)
        self.assertIsNone(plan)
    
    def test_prioritize_objects(self):
        """测试目标优先级排序"""
        sorted_objects = self.planner._prioritize_objects(self.detected_objects)
        
        # 应该按优先级排序：人(10) > 汽车(9) > 自行车(8)
        expected_order = [0, 2, 1]  # class顺序
        actual_order = [obj['class'] for obj in sorted_objects]
        
        self.assertEqual(actual_order, expected_order)
    
    def test_prioritize_with_context(self):
        """测试带上下文的优先级排序"""
        context = {
            'urgent_classes': [1]  # 自行车紧急
        }
        
        sorted_objects = self.planner._prioritize_objects(self.detected_objects, context)
        
        # 紧急类别应该优先
        self.assertEqual(sorted_objects[0]['class'], 1)
    
    def test_create_task(self):
        """测试任务创建"""
        obj = self.detected_objects[0]
        task = self.planner._create_task(obj)
        
        self.assertIsNotNone(task)
        self.assertEqual(task.target, obj)
        self.assertEqual(task.type, TaskType.APPROACH_AND_GRASP)
        self.assertIsNotNone(task.path)
    
    def test_determine_task_type(self):
        """测试任务类型确定"""
        obj = {'class': 0, 'confidence': 0.8}
        
        # 默认类型
        task_type = self.planner._determine_task_type(obj)
        self.assertEqual(task_type, TaskType.APPROACH_AND_GRASP)
        
        # 带上下文的类型
        context = {
            'task_preferences': {
                0: 'inspect'
            }
        }
        task_type = self.planner._determine_task_type(obj, context)
        self.assertEqual(task_type, TaskType.INSPECT)
    
    def test_update_task_status(self):
        """测试任务状态更新"""
        # 先创建一个任务
        plan = self.planner.plan(self.detected_objects[:1])
        task_id = plan['tasks'][0]['id']
        
        # 更新为进行中
        self.planner.update_task_status(task_id, TaskStatus.IN_PROGRESS)
        
        task = self.planner._find_task_by_id(task_id)
        self.assertEqual(task.status, TaskStatus.IN_PROGRESS)
        self.assertIsNotNone(task.start_time)
        
        # 更新为完成
        self.planner.update_task_status(task_id, TaskStatus.COMPLETED)
        
        self.assertEqual(task.status, TaskStatus.COMPLETED)
        self.assertIsNotNone(task.end_time)
        self.assertIn(task, self.planner.completed_tasks)
    
    def test_update_nonexistent_task_status(self):
        """测试更新不存在任务的状态"""
        with self.assertRaises(ValidationException):
            self.planner.update_task_status("nonexistent", TaskStatus.COMPLETED)
    
    def test_retry_failed_task(self):
        """测试重试失败任务"""
        # 创建并失败一个任务
        plan = self.planner.plan(self.detected_objects[:1])
        task_id = plan['tasks'][0]['id']
        
        self.planner.update_task_status(task_id, TaskStatus.FAILED, "Test failure")
        
        # 重试任务
        success = self.planner.retry_failed_task(task_id)
        self.assertTrue(success)
        
        task = self.planner._find_task_by_id(task_id)
        self.assertEqual(task.status, TaskStatus.PENDING)
        self.assertEqual(task.retry_count, 1)
        self.assertIn(task, self.planner.task_queue)
    
    def test_retry_max_retries_exceeded(self):
        """测试超过最大重试次数"""
        plan = self.planner.plan(self.detected_objects[:1])
        task_id = plan['tasks'][0]['id']
        
        task = self.planner._find_task_by_id(task_id)
        task.retry_count = task.max_retries
        
        self.planner.update_task_status(task_id, TaskStatus.FAILED)
        
        success = self.planner.retry_failed_task(task_id)
        self.assertFalse(success)
    
    def test_is_duplicate_task(self):
        """测试重复任务检测"""
        obj1 = {
            'class': 0,
            'confidence': 0.8,
            'bbox': [100, 100, 200, 200]
        }
        obj2 = {
            'class': 0,
            'confidence': 0.7,
            'bbox': [110, 110, 210, 210]  # 重叠边界框
        }
        
        task1 = Task(TaskType.APPROACH_AND_GRASP, obj1)
        task2 = Task(TaskType.APPROACH_AND_GRASP, obj2)
        
        self.planner.task_queue.append(task1)
        
        is_duplicate = self.planner._is_duplicate_task(task2)
        self.assertTrue(is_duplicate)
    
    def test_calculate_bbox_overlap(self):
        """测试边界框重叠计算"""
        bbox1 = [100, 100, 200, 200]
        bbox2 = [150, 150, 250, 250]  # 部分重叠
        
        overlap = self.planner._calculate_bbox_overlap(bbox1, bbox2)
        self.assertGreater(overlap, 0)
        self.assertLess(overlap, 1)
        
        # 完全重叠
        overlap_full = self.planner._calculate_bbox_overlap(bbox1, bbox1)
        self.assertEqual(overlap_full, 1.0)
        
        # 无重叠
        bbox3 = [300, 300, 400, 400]
        overlap_none = self.planner._calculate_bbox_overlap(bbox1, bbox3)
        self.assertEqual(overlap_none, 0.0)
    
    def test_get_task_statistics(self):
        """测试获取任务统计"""
        # 创建一些任务
        plan = self.planner.plan(self.detected_objects)
        
        # 更新一些任务状态
        if plan and plan['tasks']:
            task_id = plan['tasks'][0]['id']
            self.planner.update_task_status(task_id, TaskStatus.COMPLETED)
        
        stats = self.planner.get_task_statistics()
        
        self.assertIn('pending_tasks', stats)
        self.assertIn('completed_tasks', stats)
        self.assertIn('failed_tasks', stats)
        self.assertIn('total_tasks', stats)
        
        self.assertGreaterEqual(stats['completed_tasks'], 1)
    
    def test_clear_completed_tasks(self):
        """测试清理已完成任务"""
        # 创建并完成一个任务
        plan = self.planner.plan(self.detected_objects[:1])
        task_id = plan['tasks'][0]['id']
        self.planner.update_task_status(task_id, TaskStatus.COMPLETED)
        
        self.assertEqual(len(self.planner.completed_tasks), 1)
        
        self.planner.clear_completed_tasks()
        self.assertEqual(len(self.planner.completed_tasks), 0)
    
    def test_get_task_by_id(self):
        """测试根据ID获取任务"""
        plan = self.planner.plan(self.detected_objects[:1])
        task_id = plan['tasks'][0]['id']
        
        task_info = self.planner.get_task_by_id(task_id)
        
        self.assertIsNotNone(task_info)
        self.assertEqual(task_info['id'], task_id)
        
        # 不存在的任务
        nonexistent = self.planner.get_task_by_id("nonexistent")
        self.assertIsNone(nonexistent)
    
    def test_cleanup_expired_tasks(self):
        """测试清理过期任务"""
        # 创建一个任务并设置为进行中
        plan = self.planner.plan(self.detected_objects[:1])
        task_id = plan['tasks'][0]['id']
        
        self.planner.update_task_status(task_id, TaskStatus.IN_PROGRESS)
        
        # 手动设置开始时间为很久以前
        task = self.planner._find_task_by_id(task_id)
        task.start_time = time.time() - 100  # 100秒前
        task.timeout = 10  # 10秒超时
        
        # 清理过期任务
        self.planner._cleanup_expired_tasks()
        
        # 任务应该被标记为失败
        self.assertEqual(task.status, TaskStatus.FAILED)
        self.assertIn(task, self.planner.failed_tasks)


class TestTaskPlannerIntegration(unittest.TestCase):
    """任务规划器集成测试"""
    
    def test_complete_planning_workflow(self):
        """测试完整规划流程"""
        config = {
            'priority_map': {0: 10, 1: 5},
            'max_concurrent_tasks': 1,
            'default_timeout': 30.0
        }
        
        planner = TaskPlanner(config)
        
        # 检测对象
        objects = [
            {'class': 1, 'confidence': 0.8, 'bbox': [100, 100, 200, 200]},
            {'class': 0, 'confidence': 0.9, 'bbox': [300, 300, 400, 400]}
        ]
        
        # 规划
        plan = planner.plan(objects)
        
        # 验证计划
        self.assertIsNotNone(plan)
        self.assertEqual(len(plan['tasks']), 1)  # max_concurrent_tasks = 1
        
        # 高优先级任务应该被选中
        selected_task = plan['tasks'][0]
        self.assertEqual(selected_task['target']['class'], 0)  # 优先级10 > 5
        
        # 执行任务
        task_id = selected_task['id']
        planner.update_task_status(task_id, TaskStatus.IN_PROGRESS)
        planner.update_task_status(task_id, TaskStatus.COMPLETED)
        
        # 检查统计
        stats = planner.get_task_statistics()
        self.assertEqual(stats['completed_tasks'], 1)


if __name__ == '__main__':
    unittest.main()
