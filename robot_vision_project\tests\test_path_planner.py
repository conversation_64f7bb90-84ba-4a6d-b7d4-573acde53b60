"""
路径规划器测试
"""
import unittest
import numpy as np
import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.planning.path_planner import PathPlanner
from src.utils.exceptions import PathPlanningException, ValidationException


class TestPathPlanner(unittest.TestCase):
    """路径规划器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.planner = PathPlanner()
        
        # 测试目标对象
        self.test_object = {
            'bbox': [100, 100, 200, 200],
            'class': 0,
            'confidence': 0.8
        }
    
    def test_init(self):
        """测试初始化"""
        planner = PathPlanner()
        
        self.assertEqual(planner.current_position.tolist(), [0.0, 0.0, 0.0])
        self.assertEqual(len(planner.obstacles), 0)
        self.assertEqual(planner.max_velocity, 1.0)
    
    def test_init_with_bounds(self):
        """测试带边界的初始化"""
        bounds = (-1.0, 1.0, -1.0, 1.0, 0.0, 2.0)
        planner = PathPlanner(workspace_bounds=bounds)
        
        self.assertEqual(planner.workspace_bounds, bounds)
    
    def test_plan_to_object_straight(self):
        """测试直线路径规划"""
        path = self.planner.plan_to_object(self.test_object, algorithm='straight')
        
        self.assertIsInstance(path, list)
        self.assertGreater(len(path), 0)
        
        # 检查路径点格式
        for point in path:
            self.assertEqual(len(point), 3)
            self.assertIsInstance(point[0], float)
    
    def test_plan_to_object_astar(self):
        """测试A*路径规划"""
        path = self.planner.plan_to_object(self.test_object, algorithm='astar')
        
        self.assertIsInstance(path, list)
        self.assertGreater(len(path), 0)
    
    def test_plan_to_object_rrt(self):
        """测试RRT路径规划"""
        path = self.planner.plan_to_object(self.test_object, algorithm='rrt')
        
        self.assertIsInstance(path, list)
        self.assertGreater(len(path), 0)
    
    def test_plan_invalid_algorithm(self):
        """测试无效算法"""
        with self.assertRaises(ValidationException):
            self.planner.plan_to_object(self.test_object, algorithm='invalid')
    
    def test_plan_invalid_object(self):
        """测试无效目标对象"""
        # 缺少bbox
        invalid_obj = {'class': 0, 'confidence': 0.8}
        with self.assertRaises(ValidationException):
            self.planner.plan_to_object(invalid_obj)
        
        # bbox格式错误
        invalid_obj = {'bbox': [100, 100], 'class': 0}
        with self.assertRaises(ValidationException):
            self.planner.plan_to_object(invalid_obj)
    
    def test_bbox_to_position(self):
        """测试边界框到位置转换"""
        bbox = [100, 100, 200, 200]
        position = self.planner._bbox_to_position(bbox)
        
        self.assertEqual(len(position), 3)
        self.assertEqual(position[0], 150)  # 中心x
        self.assertEqual(position[1], 150)  # 中心y
        self.assertGreater(position[2], 0)  # 深度应为正数
    
    def test_estimate_depth_from_bbox_size(self):
        """测试深度估计"""
        # 大边界框应该对应较近距离
        large_area = 50000
        small_area = 1000
        
        depth_large = self.planner._estimate_depth_from_bbox_size(large_area)
        depth_small = self.planner._estimate_depth_from_bbox_size(small_area)
        
        self.assertLess(depth_large, depth_small)
        
        # 测试边界情况
        zero_area = 0
        depth_zero = self.planner._estimate_depth_from_bbox_size(zero_area)
        self.assertGreater(depth_zero, 0)
    
    def test_add_obstacle(self):
        """测试添加障碍物"""
        center = [1.0, 1.0, 1.0]
        radius = 0.5
        
        self.planner.add_obstacle(center, radius)
        
        self.assertEqual(len(self.planner.obstacles), 1)
        self.assertEqual(self.planner.obstacles[0]['center'], center)
        self.assertEqual(self.planner.obstacles[0]['radius'], radius)
    
    def test_add_invalid_obstacle(self):
        """测试添加无效障碍物"""
        # 无效中心点
        with self.assertRaises(ValidationException):
            self.planner.add_obstacle([1.0, 1.0], 0.5)
        
        # 无效半径
        with self.assertRaises(ValidationException):
            self.planner.add_obstacle([1.0, 1.0, 1.0], -0.5)
    
    def test_clear_obstacles(self):
        """测试清除障碍物"""
        self.planner.add_obstacle([1.0, 1.0, 1.0], 0.5)
        self.assertEqual(len(self.planner.obstacles), 1)
        
        self.planner.clear_obstacles()
        self.assertEqual(len(self.planner.obstacles), 0)
    
    def test_update_current_position(self):
        """测试更新当前位置"""
        new_position = [1.0, 2.0, 3.0]
        self.planner.update_current_position(new_position)
        
        np.testing.assert_array_equal(self.planner.current_position, new_position)
    
    def test_update_invalid_position(self):
        """测试更新无效位置"""
        # 维度错误
        with self.assertRaises(ValidationException):
            self.planner.update_current_position([1.0, 2.0])
        
        # 超出边界
        with self.assertRaises(ValidationException):
            self.planner.update_current_position([10.0, 0.0, 0.0])
    
    def test_get_path_length(self):
        """测试计算路径长度"""
        path = [[0, 0, 0], [1, 0, 0], [1, 1, 0]]
        length = self.planner.get_path_length(path)
        
        expected_length = 2.0  # 1 + 1
        self.assertAlmostEqual(length, expected_length, places=5)
        
        # 空路径
        empty_length = self.planner.get_path_length([])
        self.assertEqual(empty_length, 0.0)
        
        # 单点路径
        single_length = self.planner.get_path_length([[0, 0, 0]])
        self.assertEqual(single_length, 0.0)
    
    def test_smooth_path(self):
        """测试路径平滑"""
        # 创建锯齿状路径
        path = [[0, 0, 0], [1, 1, 0], [2, 0, 0], [3, 1, 0]]
        smoothed = self.planner.smooth_path(path, smoothing_factor=0.5)
        
        self.assertEqual(len(smoothed), len(path))
        # 起点和终点应该保持不变
        self.assertEqual(smoothed[0], path[0])
        self.assertEqual(smoothed[-1], path[-1])
    
    def test_validate_position(self):
        """测试位置验证"""
        # 有效位置
        valid_pos = np.array([0.0, 0.0, 1.0])
        self.planner._validate_position(valid_pos)  # 不应抛出异常
        
        # 超出x边界
        with self.assertRaises(ValidationException):
            self.planner._validate_position(np.array([10.0, 0.0, 1.0]))
        
        # 超出y边界
        with self.assertRaises(ValidationException):
            self.planner._validate_position(np.array([0.0, 10.0, 1.0]))
        
        # 超出z边界
        with self.assertRaises(ValidationException):
            self.planner._validate_position(np.array([0.0, 0.0, -1.0]))
    
    def test_validate_path(self):
        """测试路径验证"""
        # 有效路径
        valid_path = [[0, 0, 1], [1, 1, 1]]
        self.planner._validate_path(valid_path)  # 不应抛出异常
        
        # 空路径
        with self.assertRaises(ValidationException):
            self.planner._validate_path([])
        
        # 路径点维度错误
        with self.assertRaises(ValidationException):
            self.planner._validate_path([[0, 0], [1, 1]])
        
        # 路径点超出边界
        with self.assertRaises(ValidationException):
            self.planner._validate_path([[10, 0, 1]])
    
    def test_collision_detection(self):
        """测试碰撞检测"""
        # 添加障碍物
        self.planner.add_obstacle([1.0, 1.0, 1.0], 0.5)
        
        # 测试碰撞路径
        collision_path = np.array([0.0, 0.0, 1.0])
        collision_end = np.array([2.0, 2.0, 1.0])
        
        has_collision = self.planner._check_collision(collision_path, collision_end)
        self.assertTrue(has_collision)
        
        # 测试无碰撞路径
        safe_start = np.array([0.0, 0.0, 0.0])
        safe_end = np.array([0.0, 0.0, 0.5])
        
        has_collision = self.planner._check_collision(safe_start, safe_end)
        self.assertFalse(has_collision)


class TestPathPlannerIntegration(unittest.TestCase):
    """路径规划器集成测试"""
    
    def test_complete_planning_workflow(self):
        """测试完整规划流程"""
        planner = PathPlanner()
        
        # 设置起始位置
        planner.update_current_position([0.0, 0.0, 0.5])
        
        # 添加障碍物
        planner.add_obstacle([1.0, 1.0, 0.5], 0.3)
        
        # 规划路径
        target_obj = {
            'bbox': [200, 200, 300, 300],
            'class': 0,
            'confidence': 0.9
        }
        
        path = planner.plan_to_object(target_obj, algorithm='straight')
        
        # 验证路径
        self.assertIsInstance(path, list)
        self.assertGreater(len(path), 0)
        
        # 计算路径长度
        length = planner.get_path_length(path)
        self.assertGreater(length, 0)
        
        # 平滑路径
        smoothed_path = planner.smooth_path(path)
        self.assertEqual(len(smoothed_path), len(path))


if __name__ == '__main__':
    unittest.main()
