# API 文档

## 核心模块 API

### ObjectDetector

目标检测器类，提供实时目标检测功能。

#### 初始化

```python
from src.vision.object_detector import ObjectDetector

config = {
    'model_path': 'yolov8n.pt',
    'confidence_threshold': 0.5,
    'input_size': [640, 480]
}
detector = ObjectDetector(config)
```

#### 方法

##### detect(image=None)
检测图像中的目标。

**参数:**
- `image` (np.ndarray, optional): 输入图像，如果为None则从摄像头获取

**返回:**
- `List[Dict[str, Any]]`: 检测到的目标列表

**示例:**
```python
import numpy as np

# 使用自定义图像
image = np.zeros((480, 640, 3), dtype=np.uint8)
objects = detector.detect(image)

# 从摄像头检测
objects = detector.detect()
```

##### detect_batch(images)
批量检测多张图像。

**参数:**
- `images` (List[np.ndarray]): 图像列表

**返回:**
- `List[List[Dict[str, Any]]]`: 每张图像的检测结果

##### set_confidence_threshold(threshold)
设置置信度阈值。

**参数:**
- `threshold` (float): 新的置信度阈值 (0.0-1.0)

##### filter_objects_by_class(objects, target_classes)
根据类别过滤检测结果。

**参数:**
- `objects` (List[Dict]): 检测结果列表
- `target_classes` (List[int]): 目标类别列表

**返回:**
- `List[Dict]`: 过滤后的检测结果

### TaskPlanner

任务规划器类，负责根据检测结果生成执行计划。

#### 初始化

```python
from src.planning.task_planner import TaskPlanner

config = {
    'priority_map': {0: 10, 1: 8, 2: 9},
    'max_concurrent_tasks': 2,
    'default_timeout': 30.0
}
planner = TaskPlanner(config)
```

#### 方法

##### plan(detected_objects, context=None)
根据检测到的目标制定任务计划。

**参数:**
- `detected_objects` (List[Dict]): 检测到的目标列表
- `context` (Dict, optional): 规划上下文信息

**返回:**
- `Dict[str, Any]`: 任务计划字典

**示例:**
```python
objects = [
    {'class': 0, 'confidence': 0.9, 'bbox': [100, 100, 200, 200]}
]
plan = planner.plan(objects)
```

##### update_task_status(task_id, status, error_message=None)
更新任务状态。

**参数:**
- `task_id` (str): 任务ID
- `status` (TaskStatus): 新状态
- `error_message` (str, optional): 错误信息

##### get_task_statistics()
获取任务统计信息。

**返回:**
- `Dict[str, Any]`: 统计信息字典

### PathPlanner

路径规划器类，提供多种路径规划算法。

#### 初始化

```python
from src.planning.path_planner import PathPlanner

# 可选择设置工作空间边界
bounds = (-5.0, 5.0, -5.0, 5.0, 0.0, 3.0)  # x_min, x_max, y_min, y_max, z_min, z_max
planner = PathPlanner(workspace_bounds=bounds)
```

#### 方法

##### plan_to_object(obj, algorithm='straight')
规划到目标的路径。

**参数:**
- `obj` (Dict): 目标对象，包含bbox信息
- `algorithm` (str): 路径规划算法 ('straight', 'astar', 'rrt')

**返回:**
- `List[List[float]]`: 路径点列表

**示例:**
```python
target_obj = {
    'bbox': [100, 100, 200, 200],
    'class': 0,
    'confidence': 0.8
}

# 直线路径
path = planner.plan_to_object(target_obj, 'straight')

# A*路径规划
path = planner.plan_to_object(target_obj, 'astar')

# RRT路径规划
path = planner.plan_to_object(target_obj, 'rrt')
```

##### add_obstacle(center, radius)
添加球形障碍物。

**参数:**
- `center` (List[float]): 障碍物中心坐标 [x, y, z]
- `radius` (float): 障碍物半径

##### update_current_position(position)
更新当前位置。

**参数:**
- `position` (List[float]): 新位置 [x, y, z]

### RobotController

机器人控制器类，负责执行任务计划。

#### 初始化

```python
from src.control.robot_controller import RobotController

config = {
    'max_speed': 1.0,
    'acceleration': 0.5,
    'position_tolerance': 0.01,
    'safety_bounds': {
        'x': [-2.0, 2.0],
        'y': [-2.0, 2.0],
        'z': [0.0, 2.0]
    }
}
controller = RobotController(config)
```

#### 方法

##### execute(plan)
执行任务计划。

**参数:**
- `plan` (Dict): 任务计划字典

**返回:**
- `bool`: 执行是否成功

**示例:**
```python
plan = {
    'tasks': [
        {
            'type': 'approach_and_grasp',
            'target': {'class': 0, 'confidence': 0.8},
            'path': [[0, 0, 0], [1, 1, 1]]
        }
    ],
    'total_tasks': 1
}

success = controller.execute(plan)
```

##### emergency_stop()
紧急停止机器人。

##### get_current_pose()
获取当前位姿。

**返回:**
- `Dict[str, Any]`: 位姿信息字典

##### get_robot_status()
获取机器人状态。

**返回:**
- `Dict[str, Any]`: 状态信息字典

## 工具模块 API

### ConfigLoader

配置加载器类，提供配置管理功能。

#### 使用方法

```python
from src.utils.config_loader import config_loader

# 加载配置
config = config_loader.load_config('config/config.yaml', enable_auto_reload=True)

# 获取配置值
value = config_loader.get_config_value('vision.confidence_threshold', default=0.5)

# 设置配置值
config_loader.set_config_value('vision.confidence_threshold', 0.7)

# 保存配置
config_loader.save_config('config/updated_config.yaml')
```

### SystemMonitor

系统监控器类，提供性能监控功能。

#### 使用方法

```python
from src.utils.monitor import system_monitor

# 启动监控
system_monitor.start_monitoring()

# 获取系统状态
status = system_monitor.get_current_status()

# 设置告警阈值
system_monitor.set_alert_threshold('cpu_percent', 85.0)

# 添加告警回调
def alert_handler(alert):
    print(f"Alert: {alert['message']}")

system_monitor.add_alert_callback(alert_handler)

# 停止监控
system_monitor.stop_monitoring()
```

### PerformanceProfiler

性能分析器类，提供性能分析功能。

#### 使用方法

```python
from src.utils.monitor import performance_profiler

# 手动计时
operation_id = performance_profiler.start_operation('my_operation')
# ... 执行操作 ...
duration = performance_profiler.end_operation(operation_id, success=True)

# 装饰器方式
@performance_profiler.profile_function('my_function')
def my_function():
    # ... 函数实现 ...
    pass

# 获取统计信息
stats = performance_profiler.get_operation_stats('my_operation')
```

## 异常处理

### 自定义异常类

```python
from src.utils.exceptions import (
    RobotVisionException,
    VisionException,
    ModelLoadException,
    DetectionException,
    PlanningException,
    ControlException
)

try:
    # 可能抛出异常的代码
    detector = ObjectDetector(invalid_config)
except ModelLoadException as e:
    print(f"模型加载失败: {e}")
except VisionException as e:
    print(f"视觉处理错误: {e}")
except RobotVisionException as e:
    print(f"系统错误: {e}")
```

### 验证函数

```python
from src.utils.exceptions import (
    validate_not_none,
    validate_positive,
    validate_range,
    validate_type
)

# 验证参数
validate_not_none(value, "parameter_name")
validate_positive(speed, "speed")
validate_range(confidence, 0.0, 1.0, "confidence")
validate_type(config, dict, "config")
```

## 日志系统

### 使用日志

```python
from src.utils.logger import RobotVisionLogger

# 获取日志器
logger = RobotVisionLogger().get_logger('MyModule')

# 记录日志
logger.info("信息日志")
logger.warning("警告日志")
logger.error("错误日志")
logger.debug("调试日志")

# 记录异常
try:
    # 可能出错的代码
    pass
except Exception as e:
    logger.log_exception(e, context={'additional': 'info'})
```

### 装饰器

```python
from src.utils.logger import log_exceptions, log_performance

@log_exceptions('MyModule')
@log_performance('MyModule')
def my_function():
    # 函数实现
    pass
```
