"""
自定义异常类
定义机器人视觉系统的专用异常
"""
from typing import Optional, Dict, Any


class RobotVisionException(Exception):
    """机器人视觉系统基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, context: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or {}
    
    def __str__(self):
        base_msg = self.message
        if self.error_code:
            base_msg = f"[{self.error_code}] {base_msg}"
        if self.context:
            base_msg += f" Context: {self.context}"
        return base_msg


class VisionException(RobotVisionException):
    """视觉处理相关异常"""
    pass


class ModelLoadException(VisionException):
    """模型加载异常"""
    pass


class ImageProcessingException(VisionException):
    """图像处理异常"""
    pass


class DetectionException(VisionException):
    """目标检测异常"""
    pass


class PlanningException(RobotVisionException):
    """规划相关异常"""
    pass


class PathPlanningException(PlanningException):
    """路径规划异常"""
    pass


class TaskPlanningException(PlanningException):
    """任务规划异常"""
    pass


class ControlException(RobotVisionException):
    """控制相关异常"""
    pass


class MotionException(ControlException):
    """运动控制异常"""
    pass


class SafetyException(ControlException):
    """安全检查异常"""
    pass


class ConfigurationException(RobotVisionException):
    """配置相关异常"""
    pass


class ValidationException(RobotVisionException):
    """验证异常"""
    pass


class CameraException(VisionException):
    """摄像头相关异常"""
    pass


class CommunicationException(RobotVisionException):
    """通信异常"""
    pass


class TimeoutException(RobotVisionException):
    """超时异常"""
    pass


class ResourceException(RobotVisionException):
    """资源相关异常"""
    pass


class CalibrationException(VisionException):
    """标定异常"""
    pass


# 异常处理工具函数
def handle_exception(exception: Exception, logger=None, reraise: bool = True):
    """统一异常处理函数"""
    if logger:
        logger.error(f"Exception handled: {type(exception).__name__}: {str(exception)}")
    
    if reraise:
        raise exception


def validate_not_none(value, name: str, exception_class=ValidationException):
    """验证值不为None"""
    if value is None:
        raise exception_class(f"{name} cannot be None")
    return value


def validate_positive(value: float, name: str, exception_class=ValidationException):
    """验证值为正数"""
    if value <= 0:
        raise exception_class(f"{name} must be positive, got {value}")
    return value


def validate_range(value: float, min_val: float, max_val: float, name: str, 
                  exception_class=ValidationException):
    """验证值在指定范围内"""
    if not (min_val <= value <= max_val):
        raise exception_class(f"{name} must be between {min_val} and {max_val}, got {value}")
    return value


def validate_type(value, expected_type, name: str, exception_class=ValidationException):
    """验证值的类型"""
    if not isinstance(value, expected_type):
        raise exception_class(f"{name} must be of type {expected_type.__name__}, got {type(value).__name__}")
    return value
