"""
配置加载器
"""
import yaml
import os
import json
import threading
import time
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
import watchdog.observers
import watchdog.events

from .logger import RobotVisionLogger, log_exceptions
from .exceptions import (
    ConfigurationException, ValidationException,
    validate_not_none, validate_type
)


class ConfigValidator:
    """配置验证器"""

    @staticmethod
    def validate_vision_config(config: Dict[str, Any]):
        """验证视觉配置"""
        required_fields = ['model_path', 'confidence_threshold']

        for field in required_fields:
            if field not in config:
                raise ValidationException(f"Vision config missing required field: {field}")

        # 验证置信度阈值
        threshold = config['confidence_threshold']
        if not (0.0 <= threshold <= 1.0):
            raise ValidationException(f"confidence_threshold must be between 0 and 1, got {threshold}")

        # 验证模型路径
        model_path = config['model_path']
        if not isinstance(model_path, str) or not model_path.strip():
            raise ValidationException("model_path must be a non-empty string")

    @staticmethod
    def validate_planning_config(config: Dict[str, Any]):
        """验证规划配置"""
        if 'priority_map' in config:
            priority_map = config['priority_map']
            if not isinstance(priority_map, dict):
                raise ValidationException("priority_map must be a dictionary")

            for key, value in priority_map.items():
                if not isinstance(value, (int, float)):
                    raise ValidationException(f"priority_map values must be numeric, got {type(value)} for key {key}")

    @staticmethod
    def validate_control_config(config: Dict[str, Any]):
        """验证控制配置"""
        numeric_fields = ['max_speed', 'acceleration']

        for field in numeric_fields:
            if field in config:
                value = config[field]
                if not isinstance(value, (int, float)) or value <= 0:
                    raise ValidationException(f"{field} must be a positive number, got {value}")


class ConfigWatcher(watchdog.events.FileSystemEventHandler):
    """配置文件监控器"""

    def __init__(self, config_loader):
        self.config_loader = config_loader
        self.logger = RobotVisionLogger().get_logger('ConfigWatcher')

    def on_modified(self, event):
        """文件修改事件处理"""
        if not event.is_directory and event.src_path == self.config_loader.config_path:
            self.logger.info(f"Config file modified: {event.src_path}")
            try:
                self.config_loader.reload_config()
            except Exception as e:
                self.logger.error(f"Failed to reload config: {str(e)}")


class ConfigLoader:
    """增强的配置加载器"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, '_initialized'):
            return

        self.logger = RobotVisionLogger().get_logger('ConfigLoader')
        self.config = {}
        self.config_path = None
        self.default_config = self._get_default_config()
        self.validator = ConfigValidator()

        # 文件监控
        self.observer = None
        self.watcher = None
        self.auto_reload = False

        # 配置缓存
        self.config_cache = {}
        self.cache_timeout = 300  # 5分钟缓存超时

        self._initialized = True
        self.logger.info("ConfigLoader initialized")

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'vision': {
                'model_path': 'yolov8n.pt',
                'confidence_threshold': 0.5,
                'input_size': [640, 480],
                'camera_id': 0,
                'camera_timeout': 5.0
            },
            'planning': {
                'priority_map': {
                    0: 10,  # 人
                    1: 8,   # 自行车
                    2: 9    # 汽车
                },
                'max_concurrent_tasks': 1,
                'default_timeout': 30.0,
                'planning_horizon': 10
            },
            'control': {
                'max_speed': 1.0,
                'acceleration': 0.5,
                'position_tolerance': 0.01,
                'orientation_tolerance': 0.05,
                'control_frequency': 100.0,
                'safety_bounds': {
                    'x': [-2.0, 2.0],
                    'y': [-2.0, 2.0],
                    'z': [0.0, 2.0]
                }
            },
            'logging': {
                'level': 'INFO',
                'file': 'logs/robot_vision.log'
            }
        }

    @log_exceptions('ConfigLoader')
    def load_config(self, config_path: Optional[str] = None,
                   enable_auto_reload: bool = False) -> Dict[str, Any]:
        """
        加载配置文件

        Args:
            config_path: 配置文件路径
            enable_auto_reload: 是否启用自动重载

        Returns:
            配置字典
        """
        try:
            # 确定配置文件路径
            if config_path is None:
                config_path = os.path.join(
                    os.path.dirname(__file__),
                    '../../config/config.yaml'
                )

            config_path = os.path.abspath(config_path)
            self.config_path = config_path

            # 检查缓存
            cache_key = f"{config_path}_{os.path.getmtime(config_path)}"
            if cache_key in self.config_cache:
                cache_entry = self.config_cache[cache_key]
                if time.time() - cache_entry['timestamp'] < self.cache_timeout:
                    self.logger.debug("Using cached config")
                    return cache_entry['config']

            # 加载配置
            config = self._load_config_file(config_path)

            # 合并默认配置
            merged_config = self._merge_configs(self.default_config, config)

            # 应用环境变量覆盖
            merged_config = self._apply_env_overrides(merged_config)

            # 验证配置
            self._validate_config(merged_config)

            # 缓存配置
            self.config_cache[cache_key] = {
                'config': merged_config,
                'timestamp': time.time()
            }

            # 清理旧缓存
            self._cleanup_cache()

            self.config = merged_config

            # 启用自动重载
            if enable_auto_reload and not self.auto_reload:
                self._enable_auto_reload()

            self.logger.info(f"Configuration loaded from {config_path}")
            return merged_config

        except Exception as e:
            self.logger.error(f"Failed to load config: {str(e)}")
            raise ConfigurationException(f"Config loading failed: {str(e)}")

    def _load_config_file(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        if not os.path.exists(config_path):
            raise ConfigurationException(f"Config file not found: {config_path}")

        file_ext = Path(config_path).suffix.lower()

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if file_ext in ['.yaml', '.yml']:
                    return yaml.safe_load(f) or {}
                elif file_ext == '.json':
                    return json.load(f)
                else:
                    raise ConfigurationException(f"Unsupported config file format: {file_ext}")

        except yaml.YAMLError as e:
            raise ConfigurationException(f"YAML parsing error: {str(e)}")
        except json.JSONDecodeError as e:
            raise ConfigurationException(f"JSON parsing error: {str(e)}")
        except Exception as e:
            raise ConfigurationException(f"File reading error: {str(e)}")

    def _merge_configs(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置字典"""
        merged = default.copy()

        for key, value in user.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key] = self._merge_configs(merged[key], value)
            else:
                merged[key] = value

        return merged

    def _apply_env_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """应用环境变量覆盖"""
        # 定义环境变量映射
        env_mappings = {
            'ROBOT_VISION_MODEL_PATH': ['vision', 'model_path'],
            'ROBOT_VISION_CONFIDENCE_THRESHOLD': ['vision', 'confidence_threshold'],
            'ROBOT_VISION_MAX_SPEED': ['control', 'max_speed'],
            'ROBOT_VISION_LOG_LEVEL': ['logging', 'level'],
            'ROBOT_VISION_CAMERA_ID': ['vision', 'camera_id']
        }

        for env_var, config_path in env_mappings.items():
            env_value = os.environ.get(env_var)
            if env_value is not None:
                # 设置配置值
                current = config
                for key in config_path[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]

                # 类型转换
                final_key = config_path[-1]
                try:
                    if final_key in ['confidence_threshold', 'max_speed']:
                        current[final_key] = float(env_value)
                    elif final_key in ['camera_id']:
                        current[final_key] = int(env_value)
                    else:
                        current[final_key] = env_value

                    self.logger.info(f"Applied env override: {env_var} = {env_value}")
                except ValueError as e:
                    self.logger.warning(f"Invalid env value for {env_var}: {env_value}")

        return config

    def _validate_config(self, config: Dict[str, Any]):
        """验证配置"""
        try:
            if 'vision' in config:
                self.validator.validate_vision_config(config['vision'])

            if 'planning' in config:
                self.validator.validate_planning_config(config['planning'])

            if 'control' in config:
                self.validator.validate_control_config(config['control'])

            self.logger.debug("Configuration validation passed")

        except Exception as e:
            raise ConfigurationException(f"Configuration validation failed: {str(e)}")

    def _cleanup_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []

        for key, entry in self.config_cache.items():
            if current_time - entry['timestamp'] > self.cache_timeout:
                expired_keys.append(key)

        for key in expired_keys:
            del self.config_cache[key]

        if expired_keys:
            self.logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

    def _enable_auto_reload(self):
        """启用自动重载"""
        if not self.config_path:
            return

        try:
            self.watcher = ConfigWatcher(self)
            self.observer = watchdog.observers.Observer()
            self.observer.schedule(
                self.watcher,
                os.path.dirname(self.config_path),
                recursive=False
            )
            self.observer.start()
            self.auto_reload = True

            self.logger.info("Auto-reload enabled for config file")

        except Exception as e:
            self.logger.error(f"Failed to enable auto-reload: {str(e)}")

    def _disable_auto_reload(self):
        """禁用自动重载"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.observer = None
            self.watcher = None
            self.auto_reload = False

            self.logger.info("Auto-reload disabled")

    @log_exceptions('ConfigLoader')
    def reload_config(self):
        """重新加载配置"""
        if not self.config_path:
            raise ConfigurationException("No config path set for reload")

        try:
            old_config = self.config.copy()
            new_config = self.load_config(self.config_path, enable_auto_reload=False)

            # 比较配置变化
            changes = self._compare_configs(old_config, new_config)
            if changes:
                self.logger.info(f"Config reloaded with {len(changes)} changes")
                for change in changes:
                    self.logger.debug(f"Config change: {change}")
            else:
                self.logger.debug("Config reloaded, no changes detected")

            return new_config

        except Exception as e:
            self.logger.error(f"Config reload failed: {str(e)}")
            raise ConfigurationException(f"Config reload failed: {str(e)}")

    def _compare_configs(self, old: Dict[str, Any], new: Dict[str, Any],
                        path: str = "") -> List[str]:
        """比较配置变化"""
        changes = []

        # 检查新增和修改的键
        for key, value in new.items():
            current_path = f"{path}.{key}" if path else key

            if key not in old:
                changes.append(f"Added: {current_path} = {value}")
            elif old[key] != value:
                if isinstance(value, dict) and isinstance(old[key], dict):
                    changes.extend(self._compare_configs(old[key], value, current_path))
                else:
                    changes.append(f"Changed: {current_path} = {value} (was {old[key]})")

        # 检查删除的键
        for key in old:
            if key not in new:
                current_path = f"{path}.{key}" if path else key
                changes.append(f"Removed: {current_path}")

        return changes

    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值

        Args:
            key_path: 配置键路径，如 'vision.confidence_threshold'
            default: 默认值

        Returns:
            配置值
        """
        try:
            keys = key_path.split('.')
            value = self.config

            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default

            return value

        except Exception as e:
            self.logger.error(f"Failed to get config value for {key_path}: {str(e)}")
            return default

    def set_config_value(self, key_path: str, value: Any):
        """
        设置配置值

        Args:
            key_path: 配置键路径
            value: 配置值
        """
        try:
            keys = key_path.split('.')
            current = self.config

            # 导航到父级字典
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]

            # 设置值
            current[keys[-1]] = value

            self.logger.debug(f"Config value set: {key_path} = {value}")

        except Exception as e:
            self.logger.error(f"Failed to set config value for {key_path}: {str(e)}")
            raise ConfigurationException(f"Failed to set config value: {str(e)}")

    def save_config(self, output_path: Optional[str] = None):
        """
        保存配置到文件

        Args:
            output_path: 输出文件路径，默认为当前配置文件路径
        """
        try:
            if output_path is None:
                output_path = self.config_path

            if not output_path:
                raise ConfigurationException("No output path specified")

            # 确保目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 保存配置
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False,
                         allow_unicode=True, indent=2)

            self.logger.info(f"Configuration saved to {output_path}")

        except Exception as e:
            self.logger.error(f"Failed to save config: {str(e)}")
            raise ConfigurationException(f"Config save failed: {str(e)}")

    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            'config_path': self.config_path,
            'auto_reload': self.auto_reload,
            'cache_size': len(self.config_cache),
            'last_loaded': time.time() if self.config else None
        }

    def __del__(self):
        """析构函数"""
        try:
            self._disable_auto_reload()
            self.logger.info("ConfigLoader destroyed")
        except:
            pass


# 全局配置加载器实例
config_loader = ConfigLoader()