"""
配置加载器
"""
import yaml
import os

class ConfigLoader:
    @staticmethod
    def load_config(config_path=None):
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(
                os.path.dirname(__file__), 
                '../../config/config.yaml'
            )
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        return config