"""
图像处理工具
"""
import cv2
import numpy as np

class ImageProcessor:
    @staticmethod
    def preprocess(image):
        """图像预处理"""
        # 调整大小
        image = cv2.resize(image, (640, 480))
        # 去噪
        image = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(image, (5, 5), 0)
        return image
    
    @staticmethod
    def enhance_contrast(image):
        """增强对比度"""
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        l = clahe.apply(l)
        enhanced = cv2.merge([l, a, b])
        return cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)