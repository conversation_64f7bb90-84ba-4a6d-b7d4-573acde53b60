"""
图像处理工具
"""
import cv2
import numpy as np
from typing import Tuple, Optional, List, Dict, Any
import time

from ..utils.logger import RobotVisionLogger, log_exceptions, log_performance
from ..utils.exceptions import (
    ImageProcessingException, ValidationException,
    validate_not_none, validate_positive, validate_type
)


class ImageProcessor:
    """图像处理工具类"""

    def __init__(self):
        self.logger = RobotVisionLogger().get_logger('ImageProcessor')
        self.logger.info("ImageProcessor initialized")

    @log_exceptions('ImageProcessor')
    @log_performance('ImageProcessor')
    def preprocess(self, image: np.ndarray, target_size: Tuple[int, int] = (640, 480)) -> np.ndarray:
        """
        图像预处理

        Args:
            image: 输入图像
            target_size: 目标尺寸 (width, height)

        Returns:
            预处理后的图像
        """
        try:
            self._validate_image(image)
            validate_not_none(target_size, "target_size")

            # 调整大小
            if image.shape[:2] != tuple(reversed(target_size)):
                image = cv2.resize(image, target_size)

            # 去噪
            image = cv2.GaussianBlur(image, (5, 5), 0)

            self.logger.debug(f"Image preprocessed to size {target_size}")
            return image

        except Exception as e:
            raise ImageProcessingException(f"Image preprocessing failed: {str(e)}")

    @log_exceptions('ImageProcessor')
    @log_performance('ImageProcessor')
    def enhance_contrast(self, image: np.ndarray, clip_limit: float = 2.0,
                        tile_grid_size: Tuple[int, int] = (8, 8)) -> np.ndarray:
        """
        增强图像对比度

        Args:
            image: 输入图像
            clip_limit: CLAHE剪切限制
            tile_grid_size: 瓦片网格大小

        Returns:
            对比度增强后的图像
        """
        try:
            self._validate_image(image)
            validate_positive(clip_limit, "clip_limit")

            # 转换到LAB色彩空间
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)

            # 应用CLAHE
            clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
            l = clahe.apply(l)

            # 合并通道并转换回BGR
            enhanced = cv2.merge([l, a, b])
            result = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)

            self.logger.debug("Contrast enhancement completed")
            return result

        except Exception as e:
            raise ImageProcessingException(f"Contrast enhancement failed: {str(e)}")

    @log_exceptions('ImageProcessor')
    def normalize_image(self, image: np.ndarray) -> np.ndarray:
        """图像归一化"""
        try:
            self._validate_image(image)

            # 归一化到0-255范围
            normalized = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX)

            self.logger.debug("Image normalized")
            return normalized.astype(np.uint8)

        except Exception as e:
            raise ImageProcessingException(f"Image normalization failed: {str(e)}")

    @log_exceptions('ImageProcessor')
    def denoise_image(self, image: np.ndarray, method: str = 'gaussian') -> np.ndarray:
        """
        图像去噪

        Args:
            image: 输入图像
            method: 去噪方法 ('gaussian', 'bilateral', 'median')

        Returns:
            去噪后的图像
        """
        try:
            self._validate_image(image)

            if method == 'gaussian':
                result = cv2.GaussianBlur(image, (5, 5), 0)
            elif method == 'bilateral':
                result = cv2.bilateralFilter(image, 9, 75, 75)
            elif method == 'median':
                result = cv2.medianBlur(image, 5)
            else:
                raise ValidationException(f"Unknown denoising method: {method}")

            self.logger.debug(f"Image denoised using {method} method")
            return result

        except Exception as e:
            raise ImageProcessingException(f"Image denoising failed: {str(e)}")

    @log_exceptions('ImageProcessor')
    def adjust_brightness_contrast(self, image: np.ndarray, alpha: float = 1.0,
                                 beta: int = 0) -> np.ndarray:
        """
        调整图像亮度和对比度

        Args:
            image: 输入图像
            alpha: 对比度控制 (1.0-3.0)
            beta: 亮度控制 (0-100)

        Returns:
            调整后的图像
        """
        try:
            self._validate_image(image)

            # 应用线性变换
            adjusted = cv2.convertScaleAbs(image, alpha=alpha, beta=beta)

            self.logger.debug(f"Brightness/contrast adjusted: alpha={alpha}, beta={beta}")
            return adjusted

        except Exception as e:
            raise ImageProcessingException(f"Brightness/contrast adjustment failed: {str(e)}")

    def _validate_image(self, image: np.ndarray):
        """验证图像格式"""
        validate_not_none(image, "image")

        if not isinstance(image, np.ndarray):
            raise ImageProcessingException("Image must be a numpy array")

        if len(image.shape) not in [2, 3]:
            raise ImageProcessingException("Image must be 2D or 3D array")

        if image.size == 0:
            raise ImageProcessingException("Image is empty")

        if len(image.shape) == 3 and image.shape[2] not in [1, 3, 4]:
            raise ImageProcessingException("Image must have 1, 3, or 4 channels")

    @log_exceptions('ImageProcessor')
    def detect_edges(self, image: np.ndarray, low_threshold: int = 50,
                    high_threshold: int = 150) -> np.ndarray:
        """
        边缘检测

        Args:
            image: 输入图像
            low_threshold: 低阈值
            high_threshold: 高阈值

        Returns:
            边缘检测结果
        """
        try:
            self._validate_image(image)

            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            # Canny边缘检测
            edges = cv2.Canny(gray, low_threshold, high_threshold)

            self.logger.debug("Edge detection completed")
            return edges

        except Exception as e:
            raise ImageProcessingException(f"Edge detection failed: {str(e)}")

    @log_exceptions('ImageProcessor')
    def histogram_equalization(self, image: np.ndarray) -> np.ndarray:
        """直方图均衡化"""
        try:
            self._validate_image(image)

            if len(image.shape) == 3:
                # 彩色图像：在YUV空间进行均衡化
                yuv = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)
                yuv[:,:,0] = cv2.equalizeHist(yuv[:,:,0])
                result = cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)
            else:
                # 灰度图像
                result = cv2.equalizeHist(image)

            self.logger.debug("Histogram equalization completed")
            return result

        except Exception as e:
            raise ImageProcessingException(f"Histogram equalization failed: {str(e)}")

    @log_exceptions('ImageProcessor')
    def morphological_operations(self, image: np.ndarray, operation: str = 'opening',
                               kernel_size: int = 5) -> np.ndarray:
        """
        形态学操作

        Args:
            image: 输入图像
            operation: 操作类型 ('opening', 'closing', 'erosion', 'dilation')
            kernel_size: 核大小

        Returns:
            形态学操作结果
        """
        try:
            self._validate_image(image)
            validate_positive(kernel_size, "kernel_size")

            kernel = np.ones((kernel_size, kernel_size), np.uint8)

            if operation == 'erosion':
                result = cv2.erode(image, kernel, iterations=1)
            elif operation == 'dilation':
                result = cv2.dilate(image, kernel, iterations=1)
            elif operation == 'opening':
                result = cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel)
            elif operation == 'closing':
                result = cv2.morphologyEx(image, cv2.MORPH_CLOSE, kernel)
            else:
                raise ValidationException(f"Unknown morphological operation: {operation}")

            self.logger.debug(f"Morphological {operation} completed")
            return result

        except Exception as e:
            raise ImageProcessingException(f"Morphological operation failed: {str(e)}")

    @log_exceptions('ImageProcessor')
    def calculate_image_quality(self, image: np.ndarray) -> Dict[str, float]:
        """
        计算图像质量指标

        Args:
            image: 输入图像

        Returns:
            图像质量指标字典
        """
        try:
            self._validate_image(image)

            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            # 计算各种质量指标
            # 1. 拉普拉斯方差（清晰度）
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()

            # 2. 梯度幅值（边缘强度）
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2).mean()

            # 3. 对比度
            contrast = gray.std()

            # 4. 亮度
            brightness = gray.mean()

            # 5. 熵（信息量）
            hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
            hist = hist.flatten()
            hist = hist[hist > 0]  # 移除零值
            entropy = -np.sum(hist * np.log2(hist / hist.sum()))

            quality_metrics = {
                'sharpness': float(laplacian_var),
                'edge_strength': float(gradient_magnitude),
                'contrast': float(contrast),
                'brightness': float(brightness),
                'entropy': float(entropy)
            }

            self.logger.debug(f"Image quality calculated: {quality_metrics}")
            return quality_metrics

        except Exception as e:
            raise ImageProcessingException(f"Image quality calculation failed: {str(e)}")

    @staticmethod
    def create_processor() -> 'ImageProcessor':
        """创建ImageProcessor实例的工厂方法"""
        return ImageProcessor()