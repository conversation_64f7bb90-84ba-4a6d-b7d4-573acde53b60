"""
目标检测器测试
"""
import unittest
import numpy as np
import tempfile
import os
import time
from unittest.mock import Mock, patch, MagicMock

import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.vision.object_detector import ObjectDetector
from src.utils.exceptions import ModelLoadException, DetectionException, ValidationException


class TestObjectDetector(unittest.TestCase):
    """目标检测器测试类"""

    def setUp(self):
        """测试前准备"""
        self.valid_config = {
            'model_path': 'yolov8n.pt',
            'confidence_threshold': 0.5,
            'input_size': [640, 480],
            'camera_id': 0,
            'camera_timeout': 5.0
        }

        # 创建测试图像
        self.test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        self.empty_image = np.zeros((480, 640, 3), dtype=np.uint8)

        # Mock YOLO模型
        self.mock_model = Mock()
        self.mock_results = self._create_mock_results()

    def _create_mock_results(self):
        """创建模拟检测结果"""
        mock_box = Mock()
        mock_box.conf = 0.8
        mock_box.cls = 0
        mock_box.xyxy.tolist.return_value = [[100, 100, 200, 200]]

        mock_result = Mock()
        mock_result.boxes = [mock_box]

        return [mock_result]

    @patch('src.vision.object_detector.YOLO')
    def test_init_valid_config(self, mock_yolo):
        """测试有效配置的初始化"""
        mock_yolo.return_value = self.mock_model

        detector = ObjectDetector(self.valid_config)

        self.assertEqual(detector.confidence_threshold, 0.5)
        self.assertEqual(detector.input_size, [640, 480])
        mock_yolo.assert_called_once_with('yolov8n.pt')

    def test_init_invalid_config(self):
        """测试无效配置的初始化"""
        # 测试缺少必需字段
        invalid_config = {'model_path': 'test.pt'}
        with self.assertRaises(ValidationException):
            ObjectDetector(invalid_config)

        # 测试无效置信度阈值
        invalid_config = {
            'model_path': 'test.pt',
            'confidence_threshold': 1.5
        }
        with self.assertRaises(ValidationException):
            ObjectDetector(invalid_config)

    @patch('src.vision.object_detector.YOLO')
    def test_detect_with_image(self, mock_yolo):
        """测试带图像的检测"""
        mock_yolo.return_value = self.mock_model
        self.mock_model.return_value = self.mock_results

        detector = ObjectDetector(self.valid_config)
        objects = detector.detect(self.test_image)

        self.assertIsInstance(objects, list)
        self.assertEqual(len(objects), 1)
        self.assertEqual(objects[0]['class'], 0)
        self.assertEqual(objects[0]['confidence'], 0.8)
        self.assertEqual(objects[0]['bbox'], [100, 100, 200, 200])
        self.assertIn('timestamp', objects[0])

    @patch('src.vision.object_detector.YOLO')
    @patch('cv2.VideoCapture')
    def test_detect_from_camera(self, mock_video_capture, mock_yolo):
        """测试从摄像头检测"""
        mock_yolo.return_value = self.mock_model
        self.mock_model.return_value = self.mock_results

        # Mock摄像头
        mock_cap = Mock()
        mock_cap.isOpened.return_value = True
        mock_cap.read.return_value = (True, self.test_image)
        mock_video_capture.return_value = mock_cap

        detector = ObjectDetector(self.valid_config)
        objects = detector.detect()  # 不传入图像，从摄像头获取

        self.assertIsInstance(objects, list)
        mock_video_capture.assert_called_once_with(0)
        mock_cap.release.assert_called_once()

    @patch('src.vision.object_detector.YOLO')
    def test_detect_empty_results(self, mock_yolo):
        """测试空检测结果"""
        mock_yolo.return_value = self.mock_model
        self.mock_model.return_value = []

        detector = ObjectDetector(self.valid_config)
        objects = detector.detect(self.test_image)

        self.assertIsInstance(objects, list)
        self.assertEqual(len(objects), 0)

    @patch('src.vision.object_detector.YOLO')
    def test_detect_low_confidence(self, mock_yolo):
        """测试低置信度过滤"""
        mock_yolo.return_value = self.mock_model

        # 创建低置信度结果
        mock_box = Mock()
        mock_box.conf = 0.3  # 低于阈值0.5
        mock_box.cls = 0
        mock_box.xyxy.tolist.return_value = [[100, 100, 200, 200]]

        mock_result = Mock()
        mock_result.boxes = [mock_box]
        self.mock_model.return_value = [mock_result]

        detector = ObjectDetector(self.valid_config)
        objects = detector.detect(self.test_image)

        self.assertEqual(len(objects), 0)  # 应该被过滤掉

    @patch('src.vision.object_detector.YOLO')
    def test_detect_batch(self, mock_yolo):
        """测试批量检测"""
        mock_yolo.return_value = self.mock_model
        self.mock_model.return_value = self.mock_results

        detector = ObjectDetector(self.valid_config)
        images = [self.test_image, self.empty_image]
        results = detector.detect_batch(images)

        self.assertEqual(len(results), 2)
        self.assertIsInstance(results[0], list)
        self.assertIsInstance(results[1], list)

    @patch('src.vision.object_detector.YOLO')
    def test_set_confidence_threshold(self, mock_yolo):
        """测试设置置信度阈值"""
        mock_yolo.return_value = self.mock_model

        detector = ObjectDetector(self.valid_config)

        # 测试有效阈值
        detector.set_confidence_threshold(0.7)
        self.assertEqual(detector.confidence_threshold, 0.7)

        # 测试无效阈值
        with self.assertRaises(ValidationException):
            detector.set_confidence_threshold(1.5)

        with self.assertRaises(ValidationException):
            detector.set_confidence_threshold(-0.1)

    @patch('src.vision.object_detector.YOLO')
    def test_filter_objects_by_class(self, mock_yolo):
        """测试按类别过滤"""
        mock_yolo.return_value = self.mock_model

        detector = ObjectDetector(self.valid_config)

        objects = [
            {'class': 0, 'confidence': 0.8, 'bbox': [100, 100, 200, 200]},
            {'class': 1, 'confidence': 0.7, 'bbox': [300, 300, 400, 400]},
            {'class': 0, 'confidence': 0.6, 'bbox': [500, 500, 600, 600]}
        ]

        filtered = detector.filter_objects_by_class(objects, [0])
        self.assertEqual(len(filtered), 2)

        filtered = detector.filter_objects_by_class(objects, [1])
        self.assertEqual(len(filtered), 1)

    @patch('src.vision.object_detector.YOLO')
    def test_filter_objects_by_confidence(self, mock_yolo):
        """测试按置信度过滤"""
        mock_yolo.return_value = self.mock_model

        detector = ObjectDetector(self.valid_config)

        objects = [
            {'class': 0, 'confidence': 0.8, 'bbox': [100, 100, 200, 200]},
            {'class': 1, 'confidence': 0.7, 'bbox': [300, 300, 400, 400]},
            {'class': 0, 'confidence': 0.6, 'bbox': [500, 500, 600, 600]}
        ]

        filtered = detector.filter_objects_by_confidence(objects, 0.65)
        self.assertEqual(len(filtered), 2)

    @patch('src.vision.object_detector.YOLO')
    def test_get_detection_statistics(self, mock_yolo):
        """测试获取检测统计"""
        mock_yolo.return_value = self.mock_model

        detector = ObjectDetector(self.valid_config)

        objects = [
            {'class': 0, 'confidence': 0.8, 'bbox': [100, 100, 200, 200]},
            {'class': 1, 'confidence': 0.7, 'bbox': [300, 300, 400, 400]},
            {'class': 0, 'confidence': 0.6, 'bbox': [500, 500, 600, 600]}
        ]

        stats = detector.get_detection_statistics(objects)

        self.assertEqual(stats['total_objects'], 3)
        self.assertEqual(stats['classes'][0], 2)
        self.assertEqual(stats['classes'][1], 1)
        self.assertAlmostEqual(stats['avg_confidence'], 0.7, places=2)
        self.assertEqual(stats['max_confidence'], 0.8)
        self.assertEqual(stats['min_confidence'], 0.6)

    def test_validate_image(self):
        """测试图像验证"""
        with patch('src.vision.object_detector.YOLO'):
            detector = ObjectDetector(self.valid_config)

            # 测试有效图像
            detector._validate_image(self.test_image)

            # 测试None图像
            with self.assertRaises(ValidationException):
                detector._validate_image(None)

            # 测试错误维度
            with self.assertRaises(ValidationException):
                detector._validate_image(np.zeros((100, 100)))

            # 测试错误通道数
            with self.assertRaises(ValidationException):
                detector._validate_image(np.zeros((100, 100, 2)))

    @patch('src.vision.object_detector.YOLO')
    def test_get_model_info(self, mock_yolo):
        """测试获取模型信息"""
        mock_yolo.return_value = self.mock_model

        detector = ObjectDetector(self.valid_config)
        info = detector.get_model_info()

        self.assertEqual(info['model_path'], 'yolov8n.pt')
        self.assertEqual(info['confidence_threshold'], 0.5)
        self.assertEqual(info['model_type'], 'YOLO')


class TestObjectDetectorPerformance(unittest.TestCase):
    """目标检测器性能测试"""

    @patch('src.vision.object_detector.YOLO')
    def test_detection_performance(self, mock_yolo):
        """测试检测性能"""
        config = {
            'model_path': 'yolov8n.pt',
            'confidence_threshold': 0.5
        }

        mock_model = Mock()
        mock_yolo.return_value = mock_model
        mock_model.return_value = []

        detector = ObjectDetector(config)
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)

        # 测试单次检测时间
        start_time = time.time()
        detector.detect(test_image)
        detection_time = time.time() - start_time

        # 检测时间应该在合理范围内（这里设为1秒，实际可能更快）
        self.assertLess(detection_time, 1.0)

    @patch('src.vision.object_detector.YOLO')
    def test_batch_detection_performance(self, mock_yolo):
        """测试批量检测性能"""
        config = {
            'model_path': 'yolov8n.pt',
            'confidence_threshold': 0.5
        }

        mock_model = Mock()
        mock_yolo.return_value = mock_model
        mock_model.return_value = []

        detector = ObjectDetector(config)

        # 创建多张测试图像
        images = [np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8) for _ in range(10)]

        start_time = time.time()
        results = detector.detect_batch(images)
        batch_time = time.time() - start_time

        self.assertEqual(len(results), 10)
        # 批量处理应该相对高效
        self.assertLess(batch_time, 5.0)


if __name__ == '__main__':
    unittest.main()