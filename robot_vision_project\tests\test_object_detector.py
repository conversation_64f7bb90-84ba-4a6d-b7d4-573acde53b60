"""
目标检测器测试
"""
import unittest
import numpy as np
from src.vision.object_detector import ObjectDetector

class TestObjectDetector(unittest.TestCase):
    def setUp(self):
        config = {
            'model_path': 'yolov8n.pt',
            'confidence_threshold': 0.5
        }
        self.detector = ObjectDetector(config)
    
    def test_detect(self):
        # 创建测试图像
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 测试检测功能
        objects = self.detector.detect(test_image)
        
        # 验证返回格式
        self.assertIsInstance(objects, list)

if __name__ == '__main__':
    unittest.main()