# 核心依赖
opencv-python==********
numpy==1.24.3
torch==2.0.1
torchvision==0.15.2
ultralytics==8.0.196
scikit-learn==1.3.0
matplotlib==3.7.2
pyyaml==6.0.1

# 系统监控
psutil==5.9.5
GPUtil==1.4.0

# 文件监控
watchdog==3.0.0

# 测试框架
pytest==7.4.0
pytest-cov==4.1.0
pytest-mock==3.11.1

# 代码质量
flake8==6.0.0
black==23.7.0
isort==5.12.0

# 类型检查
mypy==1.5.1

# 文档生成
sphinx==7.1.2
sphinx-rtd-theme==1.3.0

# 开发工具
jupyter==1.0.0
ipython==8.14.0

# 数据处理
pandas==2.0.3
scipy==1.11.1

# 网络通信（可选）
requests==2.31.0
websockets==11.0.3

# 序列化
pickle-mixin==1.0.2
joblib==1.3.1

# 时间处理
python-dateutil==2.8.2

# 配置管理
python-dotenv==1.0.0

# 日志增强
colorlog==6.7.0

# 性能分析
memory-profiler==0.60.0
line-profiler==4.1.1

# 并发处理
concurrent-futures==3.1.1

# 数学计算
sympy==1.12