#!/usr/bin/env python3
"""
机器人视觉系统主程序
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vision.object_detector import ObjectDetector
from planning.task_planner import TaskPlanner
from control.robot_controller import RobotController
from utils.config_loader import ConfigLoader

def main():
    # 加载配置
    config = ConfigLoader.load_config()
    
    # 初始化模块
    detector = ObjectDetector(config['vision'])
    planner = TaskPlanner(config['planning'])
    controller = RobotController(config['control'])
    
    print("机器人视觉系统启动...")
    
    # 主循环
    while True:
        # 目标检测
        objects = detector.detect()
        
        # 任务规划
        plan = planner.plan(objects)
        
        # 执行控制
        controller.execute(plan)

if __name__ == "__main__":
    main()