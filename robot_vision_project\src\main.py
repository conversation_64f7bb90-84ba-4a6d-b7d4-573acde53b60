#!/usr/bin/env python3
"""
机器人视觉系统主程序
"""
import sys
import os
import time
import signal
import argparse
from typing import Optional

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vision.object_detector import ObjectDetector
from vision.image_processor import ImageProcessor
from planning.task_planner import TaskPlanner
from control.robot_controller import RobotController
from utils.config_loader import Config<PERSON>oa<PERSON>, config_loader
from utils.logger import RobotVisionLogger, logger_instance
from utils.monitor import system_monitor, performance_profiler
from utils.exceptions import RobotVisionException


class RobotVisionSystem:
    """机器人视觉系统主类"""

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化机器人视觉系统

        Args:
            config_path: 配置文件路径
        """
        self.logger = RobotVisionLogger().get_logger('RobotVisionSystem')
        self.logger.info("Initializing Robot Vision System")

        # 系统组件
        self.detector = None
        self.image_processor = None
        self.planner = None
        self.controller = None

        # 系统状态
        self.running = False
        self.initialized = False

        try:
            # 加载配置
            self.config = config_loader.load_config(config_path, enable_auto_reload=True)
            self.logger.info("Configuration loaded successfully")

            # 初始化组件
            self._initialize_components()

            # 启动监控
            system_monitor.start_monitoring()

            self.initialized = True
            self.logger.info("Robot Vision System initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize system: {str(e)}")
            raise RobotVisionException(f"System initialization failed: {str(e)}")

    def _initialize_components(self):
        """初始化系统组件"""
        try:
            # 初始化图像处理器
            self.image_processor = ImageProcessor()
            self.logger.info("Image processor initialized")

            # 初始化目标检测器
            self.detector = ObjectDetector(self.config['vision'])
            self.logger.info("Object detector initialized")

            # 初始化任务规划器
            self.planner = TaskPlanner(self.config['planning'])
            self.logger.info("Task planner initialized")

            # 初始化机器人控制器
            self.controller = RobotController(self.config['control'])
            self.logger.info("Robot controller initialized")

        except Exception as e:
            self.logger.error(f"Component initialization failed: {str(e)}")
            raise

    def start(self):
        """启动系统"""
        if not self.initialized:
            raise RobotVisionException("System not initialized")

        if self.running:
            self.logger.warning("System already running")
            return

        self.logger.info("Starting Robot Vision System")
        self.running = True

        try:
            # 主循环
            self._main_loop()

        except KeyboardInterrupt:
            self.logger.info("Received interrupt signal, shutting down...")
        except Exception as e:
            self.logger.error(f"System error: {str(e)}")
            raise
        finally:
            self.stop()

    def _main_loop(self):
        """主循环"""
        loop_count = 0

        while self.running:
            try:
                loop_start_time = time.time()

                # 性能分析开始
                operation_id = performance_profiler.start_operation("main_loop")

                self.logger.debug(f"Starting loop iteration {loop_count}")

                # 目标检测
                detection_id = performance_profiler.start_operation("object_detection")
                objects = self.detector.detect()
                performance_profiler.end_operation(detection_id, success=True)

                self.logger.info(f"Detected {len(objects)} objects")

                # 任务规划
                planning_id = performance_profiler.start_operation("task_planning")
                plan = self.planner.plan(objects)
                performance_profiler.end_operation(planning_id, success=True)

                if plan:
                    self.logger.info(f"Generated plan with {plan['total_tasks']} tasks")

                    # 执行控制
                    control_id = performance_profiler.start_operation("task_execution")
                    success = self.controller.execute(plan)
                    performance_profiler.end_operation(control_id, success=success)

                    if success:
                        self.logger.info("Plan executed successfully")
                    else:
                        self.logger.warning("Plan execution failed")
                else:
                    self.logger.debug("No plan generated")

                # 性能分析结束
                performance_profiler.end_operation(operation_id, success=True)

                # 循环统计
                loop_duration = time.time() - loop_start_time
                self.logger.debug(f"Loop {loop_count} completed in {loop_duration:.3f}s")

                loop_count += 1

                # 短暂休眠以避免过度占用CPU
                time.sleep(0.1)

            except Exception as e:
                self.logger.error(f"Error in main loop: {str(e)}")
                performance_profiler.end_operation(operation_id, success=False, error_message=str(e))

                # 短暂休眠后继续
                time.sleep(1.0)

    def stop(self):
        """停止系统"""
        if not self.running:
            return

        self.logger.info("Stopping Robot Vision System")
        self.running = False

        try:
            # 停止监控
            system_monitor.stop_monitoring()

            # 停止控制器
            if self.controller:
                self.controller.emergency_stop()

            self.logger.info("Robot Vision System stopped")

        except Exception as e:
            self.logger.error(f"Error during shutdown: {str(e)}")

    def get_system_status(self) -> dict:
        """获取系统状态"""
        return {
            'initialized': self.initialized,
            'running': self.running,
            'components': {
                'detector': self.detector is not None,
                'planner': self.planner is not None,
                'controller': self.controller is not None,
                'image_processor': self.image_processor is not None
            },
            'monitoring': system_monitor.get_current_status(),
            'performance': performance_profiler.get_operation_stats()
        }

    def __del__(self):
        """析构函数"""
        try:
            self.stop()
        except:
            pass


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\nReceived signal {signum}, shutting down gracefully...")
    sys.exit(0)


def main():
    """主函数"""
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Robot Vision System')
    parser.add_argument('--config', '-c', type=str,
                       help='Configuration file path')
    parser.add_argument('--debug', '-d', action='store_true',
                       help='Enable debug logging')
    parser.add_argument('--status', '-s', action='store_true',
                       help='Show system status and exit')

    args = parser.parse_args()

    try:
        # 创建系统实例
        system = RobotVisionSystem(config_path=args.config)

        if args.status:
            # 显示状态并退出
            status = system.get_system_status()
            print("Robot Vision System Status:")
            print(f"  Initialized: {status['initialized']}")
            print(f"  Running: {status['running']}")
            print("  Components:")
            for name, available in status['components'].items():
                print(f"    {name}: {'✓' if available else '✗'}")
            return

        # 启动系统
        print("Robot Vision System starting...")
        print("Press Ctrl+C to stop")

        system.start()

    except Exception as e:
        print(f"System error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()